(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var Vu=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function od(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ad={exports:{}},Ws={},ld={exports:{}},O={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=Symbol.for("react.element"),ug=Symbol.for("react.portal"),cg=Symbol.for("react.fragment"),fg=Symbol.for("react.strict_mode"),dg=Symbol.for("react.profiler"),hg=Symbol.for("react.provider"),pg=Symbol.for("react.context"),mg=Symbol.for("react.forward_ref"),gg=Symbol.for("react.suspense"),yg=Symbol.for("react.memo"),vg=Symbol.for("react.lazy"),Ou=Symbol.iterator;function xg(e){return e===null||typeof e!="object"?null:(e=Ou&&e[Ou]||e["@@iterator"],typeof e=="function"?e:null)}var ud={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cd=Object.assign,fd={};function fr(e,t,n){this.props=e,this.context=t,this.refs=fd,this.updater=n||ud}fr.prototype.isReactComponent={};fr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};fr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function dd(){}dd.prototype=fr.prototype;function hl(e,t,n){this.props=e,this.context=t,this.refs=fd,this.updater=n||ud}var pl=hl.prototype=new dd;pl.constructor=hl;cd(pl,fr.prototype);pl.isPureReactComponent=!0;var Iu=Array.isArray,hd=Object.prototype.hasOwnProperty,ml={current:null},pd={key:!0,ref:!0,__self:!0,__source:!0};function md(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)hd.call(t,r)&&!pd.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:mi,type:e,key:s,ref:o,props:i,_owner:ml.current}}function wg(e,t){return{$$typeof:mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function gl(e){return typeof e=="object"&&e!==null&&e.$$typeof===mi}function Sg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var zu=/\/+/g;function vo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Sg(""+e.key):t.toString(36)}function Yi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case mi:case ug:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+vo(o,0):r,Iu(i)?(n="",e!=null&&(n=e.replace(zu,"$&/")+"/"),Yi(i,t,n,"",function(u){return u})):i!=null&&(gl(i)&&(i=wg(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(zu,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Iu(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+vo(s,a);o+=Yi(s,t,n,l,i)}else if(l=xg(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+vo(s,a++),o+=Yi(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ji(e,t,n){if(e==null)return e;var r=[],i=0;return Yi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function kg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},Xi={transition:null},Cg={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:Xi,ReactCurrentOwner:ml};function gd(){throw Error("act(...) is not supported in production builds of React.")}O.Children={map:ji,forEach:function(e,t,n){ji(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ji(e,function(){t++}),t},toArray:function(e){return ji(e,function(t){return t})||[]},only:function(e){if(!gl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};O.Component=fr;O.Fragment=cg;O.Profiler=dg;O.PureComponent=hl;O.StrictMode=fg;O.Suspense=gg;O.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cg;O.act=gd;O.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cd({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=ml.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)hd.call(t,l)&&!pd.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:mi,type:e.type,key:i,ref:s,props:r,_owner:o}};O.createContext=function(e){return e={$$typeof:pg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:hg,_context:e},e.Consumer=e};O.createElement=md;O.createFactory=function(e){var t=md.bind(null,e);return t.type=e,t};O.createRef=function(){return{current:null}};O.forwardRef=function(e){return{$$typeof:mg,render:e}};O.isValidElement=gl;O.lazy=function(e){return{$$typeof:vg,_payload:{_status:-1,_result:e},_init:kg}};O.memo=function(e,t){return{$$typeof:yg,type:e,compare:t===void 0?null:t}};O.startTransition=function(e){var t=Xi.transition;Xi.transition={};try{e()}finally{Xi.transition=t}};O.unstable_act=gd;O.useCallback=function(e,t){return Te.current.useCallback(e,t)};O.useContext=function(e){return Te.current.useContext(e)};O.useDebugValue=function(){};O.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};O.useEffect=function(e,t){return Te.current.useEffect(e,t)};O.useId=function(){return Te.current.useId()};O.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};O.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};O.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};O.useMemo=function(e,t){return Te.current.useMemo(e,t)};O.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};O.useRef=function(e){return Te.current.useRef(e)};O.useState=function(e){return Te.current.useState(e)};O.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};O.useTransition=function(){return Te.current.useTransition()};O.version="18.3.1";ld.exports=O;var C=ld.exports;const yl=od(C);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pg=C,Eg=Symbol.for("react.element"),Tg=Symbol.for("react.fragment"),jg=Object.prototype.hasOwnProperty,Ng=Pg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ag={key:!0,ref:!0,__self:!0,__source:!0};function yd(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)jg.call(t,r)&&!Ag.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Eg,type:e,key:s,ref:o,props:i,_owner:Ng.current}}Ws.Fragment=Tg;Ws.jsx=yd;Ws.jsxs=yd;ad.exports=Ws;var p=ad.exports,ra={},vd={exports:{}},ze={},xd={exports:{}},wd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(A,M){var _=A.length;A.push(M);e:for(;0<_;){var D=_-1>>>1,I=A[D];if(0<i(I,M))A[D]=M,A[_]=I,_=D;else break e}}function n(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var M=A[0],_=A.pop();if(_!==M){A[0]=_;e:for(var D=0,I=A.length,vt=I>>>1;D<vt;){var oe=2*(D+1)-1,Be=A[oe],ce=oe+1,$e=A[ce];if(0>i(Be,_))ce<I&&0>i($e,Be)?(A[D]=$e,A[ce]=_,D=ce):(A[D]=Be,A[oe]=_,D=oe);else if(ce<I&&0>i($e,_))A[D]=$e,A[ce]=_,D=ce;else break e}}return M}function i(A,M){var _=A.sortIndex-M.sortIndex;return _!==0?_:A.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,h=null,m=3,y=!1,x=!1,w=!1,P=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(A){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=A)r(u),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(u)}}function v(A){if(w=!1,f(A),!x)if(n(l)!==null)x=!0,Q(S);else{var M=n(u);M!==null&&ke(v,M.startTime-A)}}function S(A,M){x=!1,w&&(w=!1,g(T),T=-1),y=!0;var _=m;try{for(f(M),h=n(l);h!==null&&(!(h.expirationTime>M)||A&&!V());){var D=h.callback;if(typeof D=="function"){h.callback=null,m=h.priorityLevel;var I=D(h.expirationTime<=M);M=e.unstable_now(),typeof I=="function"?h.callback=I:h===n(l)&&r(l),f(M)}else r(l);h=n(l)}if(h!==null)var vt=!0;else{var oe=n(u);oe!==null&&ke(v,oe.startTime-M),vt=!1}return vt}finally{h=null,m=_,y=!1}}var k=!1,E=null,T=-1,j=5,L=-1;function V(){return!(e.unstable_now()-L<j)}function b(){if(E!==null){var A=e.unstable_now();L=A;var M=!0;try{M=E(!0,A)}finally{M?ue():(k=!1,E=null)}}else k=!1}var ue;if(typeof d=="function")ue=function(){d(b)};else if(typeof MessageChannel<"u"){var te=new MessageChannel,lt=te.port2;te.port1.onmessage=b,ue=function(){lt.postMessage(null)}}else ue=function(){P(b,0)};function Q(A){E=A,k||(k=!0,ue())}function ke(A,M){T=P(function(){A(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){x||y||(x=!0,Q(S))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(A){switch(m){case 1:case 2:case 3:var M=3;break;default:M=m}var _=m;m=M;try{return A()}finally{m=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,M){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var _=m;m=A;try{return M()}finally{m=_}},e.unstable_scheduleCallback=function(A,M,_){var D=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?D+_:D):_=D,A){case 1:var I=-1;break;case 2:I=250;break;case 5:I=**********;break;case 4:I=1e4;break;default:I=5e3}return I=_+I,A={id:c++,callback:M,priorityLevel:A,startTime:_,expirationTime:I,sortIndex:-1},_>D?(A.sortIndex=_,t(u,A),n(l)===null&&A===n(u)&&(w?(g(T),T=-1):w=!0,ke(v,_-D))):(A.sortIndex=I,t(l,A),x||y||(x=!0,Q(S))),A},e.unstable_shouldYield=V,e.unstable_wrapCallback=function(A){var M=m;return function(){var _=m;m=M;try{return A.apply(this,arguments)}finally{m=_}}}})(wd);xd.exports=wd;var Lg=xd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rg=C,Oe=Lg;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sd=new Set,Qr={};function Nn(e,t){nr(e,t),nr(e+"Capture",t)}function nr(e,t){for(Qr[e]=t,e=0;e<t.length;e++)Sd.add(t[e])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ia=Object.prototype.hasOwnProperty,Mg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,bu={},Bu={};function Dg(e){return ia.call(Bu,e)?!0:ia.call(bu,e)?!1:Mg.test(e)?Bu[e]=!0:(bu[e]=!0,!1)}function Fg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function _g(e,t,n,r){if(t===null||typeof t>"u"||Fg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ge[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ge[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ge[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ge[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ge[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ge[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ge[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ge[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ge[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var vl=/[\-:]([a-z])/g;function xl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vl,xl);ge[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vl,xl);ge[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vl,xl);ge[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ge[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});ge.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ge[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function wl(e,t,n,r){var i=ge.hasOwnProperty(t)?ge[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(_g(t,n,i,r)&&(n=null),r||i===null?Dg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Ft=Rg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ni=Symbol.for("react.element"),Fn=Symbol.for("react.portal"),_n=Symbol.for("react.fragment"),Sl=Symbol.for("react.strict_mode"),sa=Symbol.for("react.profiler"),kd=Symbol.for("react.provider"),Cd=Symbol.for("react.context"),kl=Symbol.for("react.forward_ref"),oa=Symbol.for("react.suspense"),aa=Symbol.for("react.suspense_list"),Cl=Symbol.for("react.memo"),bt=Symbol.for("react.lazy"),Pd=Symbol.for("react.offscreen"),$u=Symbol.iterator;function yr(e){return e===null||typeof e!="object"?null:(e=$u&&e[$u]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,xo;function Ar(e){if(xo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);xo=t&&t[1]||""}return`
`+xo+e}var wo=!1;function So(e,t){if(!e||wo)return"";wo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{wo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ar(e):""}function Vg(e){switch(e.tag){case 5:return Ar(e.type);case 16:return Ar("Lazy");case 13:return Ar("Suspense");case 19:return Ar("SuspenseList");case 0:case 2:case 15:return e=So(e.type,!1),e;case 11:return e=So(e.type.render,!1),e;case 1:return e=So(e.type,!0),e;default:return""}}function la(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _n:return"Fragment";case Fn:return"Portal";case sa:return"Profiler";case Sl:return"StrictMode";case oa:return"Suspense";case aa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cd:return(e.displayName||"Context")+".Consumer";case kd:return(e._context.displayName||"Context")+".Provider";case kl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Cl:return t=e.displayName||null,t!==null?t:la(e.type)||"Memo";case bt:t=e._payload,e=e._init;try{return la(e(t))}catch{}}return null}function Og(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return la(t);case 8:return t===Sl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function nn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ed(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ig(e){var t=Ed(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ai(e){e._valueTracker||(e._valueTracker=Ig(e))}function Td(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ed(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ds(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ua(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Uu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=nn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function jd(e,t){t=t.checked,t!=null&&wl(e,"checked",t,!1)}function ca(e,t){jd(e,t);var n=nn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?fa(e,t.type,n):t.hasOwnProperty("defaultValue")&&fa(e,t.type,nn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Hu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function fa(e,t,n){(t!=="number"||ds(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Lr=Array.isArray;function Xn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+nn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function da(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Wu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(Lr(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:nn(n)}}function Nd(e,t){var n=nn(t.value),r=nn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Gu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ad(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ha(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ad(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Li,Ld=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Li=Li||document.createElement("div"),Li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Yr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var _r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},zg=["Webkit","ms","Moz","O"];Object.keys(_r).forEach(function(e){zg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),_r[t]=_r[e]})});function Rd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||_r.hasOwnProperty(e)&&_r[e]?(""+t).trim():t+"px"}function Md(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Rd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var bg=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function pa(e,t){if(t){if(bg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function ma(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ga=null;function Pl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ya=null,Zn=null,qn=null;function Ku(e){if(e=vi(e)){if(typeof ya!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Xs(t),ya(e.stateNode,e.type,t))}}function Dd(e){Zn?qn?qn.push(e):qn=[e]:Zn=e}function Fd(){if(Zn){var e=Zn,t=qn;if(qn=Zn=null,Ku(e),t)for(e=0;e<t.length;e++)Ku(t[e])}}function _d(e,t){return e(t)}function Vd(){}var ko=!1;function Od(e,t,n){if(ko)return e(t,n);ko=!0;try{return _d(e,t,n)}finally{ko=!1,(Zn!==null||qn!==null)&&(Vd(),Fd())}}function Xr(e,t){var n=e.stateNode;if(n===null)return null;var r=Xs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var va=!1;if(Nt)try{var vr={};Object.defineProperty(vr,"passive",{get:function(){va=!0}}),window.addEventListener("test",vr,vr),window.removeEventListener("test",vr,vr)}catch{va=!1}function Bg(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Vr=!1,hs=null,ps=!1,xa=null,$g={onError:function(e){Vr=!0,hs=e}};function Ug(e,t,n,r,i,s,o,a,l){Vr=!1,hs=null,Bg.apply($g,arguments)}function Hg(e,t,n,r,i,s,o,a,l){if(Ug.apply(this,arguments),Vr){if(Vr){var u=hs;Vr=!1,hs=null}else throw Error(N(198));ps||(ps=!0,xa=u)}}function An(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Id(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Qu(e){if(An(e)!==e)throw Error(N(188))}function Wg(e){var t=e.alternate;if(!t){if(t=An(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Qu(i),e;if(s===r)return Qu(i),t;s=s.sibling}throw Error(N(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function zd(e){return e=Wg(e),e!==null?bd(e):null}function bd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=bd(e);if(t!==null)return t;e=e.sibling}return null}var Bd=Oe.unstable_scheduleCallback,Yu=Oe.unstable_cancelCallback,Gg=Oe.unstable_shouldYield,Kg=Oe.unstable_requestPaint,ne=Oe.unstable_now,Qg=Oe.unstable_getCurrentPriorityLevel,El=Oe.unstable_ImmediatePriority,$d=Oe.unstable_UserBlockingPriority,ms=Oe.unstable_NormalPriority,Yg=Oe.unstable_LowPriority,Ud=Oe.unstable_IdlePriority,Gs=null,ht=null;function Xg(e){if(ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(Gs,e,void 0,(e.current.flags&128)===128)}catch{}}var st=Math.clz32?Math.clz32:Jg,Zg=Math.log,qg=Math.LN2;function Jg(e){return e>>>=0,e===0?32:31-(Zg(e)/qg|0)|0}var Ri=64,Mi=4194304;function Rr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function gs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=Rr(a):(s&=o,s!==0&&(r=Rr(s)))}else o=n&~i,o!==0?r=Rr(o):s!==0&&(r=Rr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-st(t),i=1<<n,r|=e[n],t&=~i;return r}function ey(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ty(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-st(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=ey(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function wa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hd(){var e=Ri;return Ri<<=1,!(Ri&4194240)&&(Ri=64),e}function Co(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-st(t),e[t]=n}function ny(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-st(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Tl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var B=0;function Wd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gd,jl,Kd,Qd,Yd,Sa=!1,Di=[],Kt=null,Qt=null,Yt=null,Zr=new Map,qr=new Map,Ut=[],ry="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Xu(e,t){switch(e){case"focusin":case"focusout":Kt=null;break;case"dragenter":case"dragleave":Qt=null;break;case"mouseover":case"mouseout":Yt=null;break;case"pointerover":case"pointerout":Zr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":qr.delete(t.pointerId)}}function xr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=vi(t),t!==null&&jl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function iy(e,t,n,r,i){switch(t){case"focusin":return Kt=xr(Kt,e,t,n,r,i),!0;case"dragenter":return Qt=xr(Qt,e,t,n,r,i),!0;case"mouseover":return Yt=xr(Yt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Zr.set(s,xr(Zr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,qr.set(s,xr(qr.get(s)||null,e,t,n,r,i)),!0}return!1}function Xd(e){var t=gn(e.target);if(t!==null){var n=An(t);if(n!==null){if(t=n.tag,t===13){if(t=Id(n),t!==null){e.blockedOn=t,Yd(e.priority,function(){Kd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ka(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ga=r,n.target.dispatchEvent(r),ga=null}else return t=vi(n),t!==null&&jl(t),e.blockedOn=n,!1;t.shift()}return!0}function Zu(e,t,n){Zi(e)&&n.delete(t)}function sy(){Sa=!1,Kt!==null&&Zi(Kt)&&(Kt=null),Qt!==null&&Zi(Qt)&&(Qt=null),Yt!==null&&Zi(Yt)&&(Yt=null),Zr.forEach(Zu),qr.forEach(Zu)}function wr(e,t){e.blockedOn===t&&(e.blockedOn=null,Sa||(Sa=!0,Oe.unstable_scheduleCallback(Oe.unstable_NormalPriority,sy)))}function Jr(e){function t(i){return wr(i,e)}if(0<Di.length){wr(Di[0],e);for(var n=1;n<Di.length;n++){var r=Di[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Kt!==null&&wr(Kt,e),Qt!==null&&wr(Qt,e),Yt!==null&&wr(Yt,e),Zr.forEach(t),qr.forEach(t),n=0;n<Ut.length;n++)r=Ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ut.length&&(n=Ut[0],n.blockedOn===null);)Xd(n),n.blockedOn===null&&Ut.shift()}var Jn=Ft.ReactCurrentBatchConfig,ys=!0;function oy(e,t,n,r){var i=B,s=Jn.transition;Jn.transition=null;try{B=1,Nl(e,t,n,r)}finally{B=i,Jn.transition=s}}function ay(e,t,n,r){var i=B,s=Jn.transition;Jn.transition=null;try{B=4,Nl(e,t,n,r)}finally{B=i,Jn.transition=s}}function Nl(e,t,n,r){if(ys){var i=ka(e,t,n,r);if(i===null)Do(e,t,r,vs,n),Xu(e,r);else if(iy(i,e,t,n,r))r.stopPropagation();else if(Xu(e,r),t&4&&-1<ry.indexOf(e)){for(;i!==null;){var s=vi(i);if(s!==null&&Gd(s),s=ka(e,t,n,r),s===null&&Do(e,t,r,vs,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Do(e,t,r,null,n)}}var vs=null;function ka(e,t,n,r){if(vs=null,e=Pl(r),e=gn(e),e!==null)if(t=An(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Id(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vs=e,null}function Zd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qg()){case El:return 1;case $d:return 4;case ms:case Yg:return 16;case Ud:return 536870912;default:return 16}default:return 16}}var Wt=null,Al=null,qi=null;function qd(){if(qi)return qi;var e,t=Al,n=t.length,r,i="value"in Wt?Wt.value:Wt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return qi=i.slice(e,1<r?1-r:void 0)}function Ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Fi(){return!0}function qu(){return!1}function be(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Fi:qu,this.isPropagationStopped=qu,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Fi)},persist:function(){},isPersistent:Fi}),t}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ll=be(dr),yi=J({},dr,{view:0,detail:0}),ly=be(yi),Po,Eo,Sr,Ks=J({},yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Rl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Sr&&(Sr&&e.type==="mousemove"?(Po=e.screenX-Sr.screenX,Eo=e.screenY-Sr.screenY):Eo=Po=0,Sr=e),Po)},movementY:function(e){return"movementY"in e?e.movementY:Eo}}),Ju=be(Ks),uy=J({},Ks,{dataTransfer:0}),cy=be(uy),fy=J({},yi,{relatedTarget:0}),To=be(fy),dy=J({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),hy=be(dy),py=J({},dr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),my=be(py),gy=J({},dr,{data:0}),ec=be(gy),yy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},vy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=xy[e])?!!t[e]:!1}function Rl(){return wy}var Sy=J({},yi,{key:function(e){if(e.key){var t=yy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?vy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Rl,charCode:function(e){return e.type==="keypress"?Ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ky=be(Sy),Cy=J({},Ks,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),tc=be(Cy),Py=J({},yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Rl}),Ey=be(Py),Ty=J({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),jy=be(Ty),Ny=J({},Ks,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ay=be(Ny),Ly=[9,13,27,32],Ml=Nt&&"CompositionEvent"in window,Or=null;Nt&&"documentMode"in document&&(Or=document.documentMode);var Ry=Nt&&"TextEvent"in window&&!Or,Jd=Nt&&(!Ml||Or&&8<Or&&11>=Or),nc=" ",rc=!1;function eh(e,t){switch(e){case"keyup":return Ly.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function th(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vn=!1;function My(e,t){switch(e){case"compositionend":return th(t);case"keypress":return t.which!==32?null:(rc=!0,nc);case"textInput":return e=t.data,e===nc&&rc?null:e;default:return null}}function Dy(e,t){if(Vn)return e==="compositionend"||!Ml&&eh(e,t)?(e=qd(),qi=Al=Wt=null,Vn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jd&&t.locale!=="ko"?null:t.data;default:return null}}var Fy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ic(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Fy[e.type]:t==="textarea"}function nh(e,t,n,r){Dd(r),t=xs(t,"onChange"),0<t.length&&(n=new Ll("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ir=null,ei=null;function _y(e){hh(e,0)}function Qs(e){var t=zn(e);if(Td(t))return e}function Vy(e,t){if(e==="change")return t}var rh=!1;if(Nt){var jo;if(Nt){var No="oninput"in document;if(!No){var sc=document.createElement("div");sc.setAttribute("oninput","return;"),No=typeof sc.oninput=="function"}jo=No}else jo=!1;rh=jo&&(!document.documentMode||9<document.documentMode)}function oc(){Ir&&(Ir.detachEvent("onpropertychange",ih),ei=Ir=null)}function ih(e){if(e.propertyName==="value"&&Qs(ei)){var t=[];nh(t,ei,e,Pl(e)),Od(_y,t)}}function Oy(e,t,n){e==="focusin"?(oc(),Ir=t,ei=n,Ir.attachEvent("onpropertychange",ih)):e==="focusout"&&oc()}function Iy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Qs(ei)}function zy(e,t){if(e==="click")return Qs(t)}function by(e,t){if(e==="input"||e==="change")return Qs(t)}function By(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:By;function ti(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ia.call(t,i)||!at(e[i],t[i]))return!1}return!0}function ac(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function lc(e,t){var n=ac(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ac(n)}}function sh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function oh(){for(var e=window,t=ds();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ds(e.document)}return t}function Dl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function $y(e){var t=oh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sh(n.ownerDocument.documentElement,n)){if(r!==null&&Dl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=lc(n,s);var o=lc(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Uy=Nt&&"documentMode"in document&&11>=document.documentMode,On=null,Ca=null,zr=null,Pa=!1;function uc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Pa||On==null||On!==ds(r)||(r=On,"selectionStart"in r&&Dl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zr&&ti(zr,r)||(zr=r,r=xs(Ca,"onSelect"),0<r.length&&(t=new Ll("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=On)))}function _i(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var In={animationend:_i("Animation","AnimationEnd"),animationiteration:_i("Animation","AnimationIteration"),animationstart:_i("Animation","AnimationStart"),transitionend:_i("Transition","TransitionEnd")},Ao={},ah={};Nt&&(ah=document.createElement("div").style,"AnimationEvent"in window||(delete In.animationend.animation,delete In.animationiteration.animation,delete In.animationstart.animation),"TransitionEvent"in window||delete In.transitionend.transition);function Ys(e){if(Ao[e])return Ao[e];if(!In[e])return e;var t=In[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ah)return Ao[e]=t[n];return e}var lh=Ys("animationend"),uh=Ys("animationiteration"),ch=Ys("animationstart"),fh=Ys("transitionend"),dh=new Map,cc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function an(e,t){dh.set(e,t),Nn(t,[e])}for(var Lo=0;Lo<cc.length;Lo++){var Ro=cc[Lo],Hy=Ro.toLowerCase(),Wy=Ro[0].toUpperCase()+Ro.slice(1);an(Hy,"on"+Wy)}an(lh,"onAnimationEnd");an(uh,"onAnimationIteration");an(ch,"onAnimationStart");an("dblclick","onDoubleClick");an("focusin","onFocus");an("focusout","onBlur");an(fh,"onTransitionEnd");nr("onMouseEnter",["mouseout","mouseover"]);nr("onMouseLeave",["mouseout","mouseover"]);nr("onPointerEnter",["pointerout","pointerover"]);nr("onPointerLeave",["pointerout","pointerover"]);Nn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Nn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Nn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Nn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Nn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Nn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Gy=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function fc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Hg(r,t,void 0,e),e.currentTarget=null}function hh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;fc(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;fc(i,a,u),s=l}}}if(ps)throw e=xa,ps=!1,xa=null,e}function U(e,t){var n=t[Aa];n===void 0&&(n=t[Aa]=new Set);var r=e+"__bubble";n.has(r)||(ph(t,e,2,!1),n.add(r))}function Mo(e,t,n){var r=0;t&&(r|=4),ph(n,e,r,t)}var Vi="_reactListening"+Math.random().toString(36).slice(2);function ni(e){if(!e[Vi]){e[Vi]=!0,Sd.forEach(function(n){n!=="selectionchange"&&(Gy.has(n)||Mo(n,!1,e),Mo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Vi]||(t[Vi]=!0,Mo("selectionchange",!1,t))}}function ph(e,t,n,r){switch(Zd(t)){case 1:var i=oy;break;case 4:i=ay;break;default:i=Nl}n=i.bind(null,t,n,e),i=void 0,!va||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Do(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=gn(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}Od(function(){var u=s,c=Pl(n),h=[];e:{var m=dh.get(e);if(m!==void 0){var y=Ll,x=e;switch(e){case"keypress":if(Ji(n)===0)break e;case"keydown":case"keyup":y=ky;break;case"focusin":x="focus",y=To;break;case"focusout":x="blur",y=To;break;case"beforeblur":case"afterblur":y=To;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Ju;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=cy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Ey;break;case lh:case uh:case ch:y=hy;break;case fh:y=jy;break;case"scroll":y=ly;break;case"wheel":y=Ay;break;case"copy":case"cut":case"paste":y=my;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=tc}var w=(t&4)!==0,P=!w&&e==="scroll",g=w?m!==null?m+"Capture":null:m;w=[];for(var d=u,f;d!==null;){f=d;var v=f.stateNode;if(f.tag===5&&v!==null&&(f=v,g!==null&&(v=Xr(d,g),v!=null&&w.push(ri(d,v,f)))),P)break;d=d.return}0<w.length&&(m=new y(m,x,null,n,c),h.push({event:m,listeners:w}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",m&&n!==ga&&(x=n.relatedTarget||n.fromElement)&&(gn(x)||x[At]))break e;if((y||m)&&(m=c.window===c?c:(m=c.ownerDocument)?m.defaultView||m.parentWindow:window,y?(x=n.relatedTarget||n.toElement,y=u,x=x?gn(x):null,x!==null&&(P=An(x),x!==P||x.tag!==5&&x.tag!==6)&&(x=null)):(y=null,x=u),y!==x)){if(w=Ju,v="onMouseLeave",g="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(w=tc,v="onPointerLeave",g="onPointerEnter",d="pointer"),P=y==null?m:zn(y),f=x==null?m:zn(x),m=new w(v,d+"leave",y,n,c),m.target=P,m.relatedTarget=f,v=null,gn(c)===u&&(w=new w(g,d+"enter",x,n,c),w.target=f,w.relatedTarget=P,v=w),P=v,y&&x)t:{for(w=y,g=x,d=0,f=w;f;f=Dn(f))d++;for(f=0,v=g;v;v=Dn(v))f++;for(;0<d-f;)w=Dn(w),d--;for(;0<f-d;)g=Dn(g),f--;for(;d--;){if(w===g||g!==null&&w===g.alternate)break t;w=Dn(w),g=Dn(g)}w=null}else w=null;y!==null&&dc(h,m,y,w,!1),x!==null&&P!==null&&dc(h,P,x,w,!0)}}e:{if(m=u?zn(u):window,y=m.nodeName&&m.nodeName.toLowerCase(),y==="select"||y==="input"&&m.type==="file")var S=Vy;else if(ic(m))if(rh)S=by;else{S=Iy;var k=Oy}else(y=m.nodeName)&&y.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(S=zy);if(S&&(S=S(e,u))){nh(h,S,n,c);break e}k&&k(e,m,u),e==="focusout"&&(k=m._wrapperState)&&k.controlled&&m.type==="number"&&fa(m,"number",m.value)}switch(k=u?zn(u):window,e){case"focusin":(ic(k)||k.contentEditable==="true")&&(On=k,Ca=u,zr=null);break;case"focusout":zr=Ca=On=null;break;case"mousedown":Pa=!0;break;case"contextmenu":case"mouseup":case"dragend":Pa=!1,uc(h,n,c);break;case"selectionchange":if(Uy)break;case"keydown":case"keyup":uc(h,n,c)}var E;if(Ml)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else Vn?eh(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Jd&&n.locale!=="ko"&&(Vn||T!=="onCompositionStart"?T==="onCompositionEnd"&&Vn&&(E=qd()):(Wt=c,Al="value"in Wt?Wt.value:Wt.textContent,Vn=!0)),k=xs(u,T),0<k.length&&(T=new ec(T,e,null,n,c),h.push({event:T,listeners:k}),E?T.data=E:(E=th(n),E!==null&&(T.data=E)))),(E=Ry?My(e,n):Dy(e,n))&&(u=xs(u,"onBeforeInput"),0<u.length&&(c=new ec("onBeforeInput","beforeinput",null,n,c),h.push({event:c,listeners:u}),c.data=E))}hh(h,t)})}function ri(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xs(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Xr(e,n),s!=null&&r.unshift(ri(e,s,i)),s=Xr(e,t),s!=null&&r.push(ri(e,s,i))),e=e.return}return r}function Dn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function dc(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Xr(n,s),l!=null&&o.unshift(ri(n,l,a))):i||(l=Xr(n,s),l!=null&&o.push(ri(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Ky=/\r\n?/g,Qy=/\u0000|\uFFFD/g;function hc(e){return(typeof e=="string"?e:""+e).replace(Ky,`
`).replace(Qy,"")}function Oi(e,t,n){if(t=hc(t),hc(e)!==t&&n)throw Error(N(425))}function ws(){}var Ea=null,Ta=null;function ja(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Na=typeof setTimeout=="function"?setTimeout:void 0,Yy=typeof clearTimeout=="function"?clearTimeout:void 0,pc=typeof Promise=="function"?Promise:void 0,Xy=typeof queueMicrotask=="function"?queueMicrotask:typeof pc<"u"?function(e){return pc.resolve(null).then(e).catch(Zy)}:Na;function Zy(e){setTimeout(function(){throw e})}function Fo(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Jr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Jr(t)}function Xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function mc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var hr=Math.random().toString(36).slice(2),dt="__reactFiber$"+hr,ii="__reactProps$"+hr,At="__reactContainer$"+hr,Aa="__reactEvents$"+hr,qy="__reactListeners$"+hr,Jy="__reactHandles$"+hr;function gn(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[At]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=mc(e);e!==null;){if(n=e[dt])return n;e=mc(e)}return t}e=n,n=e.parentNode}return null}function vi(e){return e=e[dt]||e[At],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function zn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Xs(e){return e[ii]||null}var La=[],bn=-1;function ln(e){return{current:e}}function H(e){0>bn||(e.current=La[bn],La[bn]=null,bn--)}function $(e,t){bn++,La[bn]=e.current,e.current=t}var rn={},Se=ln(rn),Le=ln(!1),Cn=rn;function rr(e,t){var n=e.type.contextTypes;if(!n)return rn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Re(e){return e=e.childContextTypes,e!=null}function Ss(){H(Le),H(Se)}function gc(e,t,n){if(Se.current!==rn)throw Error(N(168));$(Se,t),$(Le,n)}function mh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(N(108,Og(e)||"Unknown",i));return J({},n,r)}function ks(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||rn,Cn=Se.current,$(Se,e),$(Le,Le.current),!0}function yc(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=mh(e,t,Cn),r.__reactInternalMemoizedMergedChildContext=e,H(Le),H(Se),$(Se,e)):H(Le),$(Le,n)}var wt=null,Zs=!1,_o=!1;function gh(e){wt===null?wt=[e]:wt.push(e)}function ev(e){Zs=!0,gh(e)}function un(){if(!_o&&wt!==null){_o=!0;var e=0,t=B;try{var n=wt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}wt=null,Zs=!1}catch(i){throw wt!==null&&(wt=wt.slice(e+1)),Bd(El,un),i}finally{B=t,_o=!1}}return null}var Bn=[],$n=0,Cs=null,Ps=0,Ge=[],Ke=0,Pn=null,St=1,kt="";function dn(e,t){Bn[$n++]=Ps,Bn[$n++]=Cs,Cs=e,Ps=t}function yh(e,t,n){Ge[Ke++]=St,Ge[Ke++]=kt,Ge[Ke++]=Pn,Pn=e;var r=St;e=kt;var i=32-st(r)-1;r&=~(1<<i),n+=1;var s=32-st(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,St=1<<32-st(t)+i|n<<i|r,kt=s+e}else St=1<<s|n<<i|r,kt=e}function Fl(e){e.return!==null&&(dn(e,1),yh(e,1,0))}function _l(e){for(;e===Cs;)Cs=Bn[--$n],Bn[$n]=null,Ps=Bn[--$n],Bn[$n]=null;for(;e===Pn;)Pn=Ge[--Ke],Ge[Ke]=null,kt=Ge[--Ke],Ge[Ke]=null,St=Ge[--Ke],Ge[Ke]=null}var Ve=null,_e=null,K=!1,rt=null;function vh(e,t){var n=Qe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function vc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ve=e,_e=Xt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ve=e,_e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pn!==null?{id:St,overflow:kt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Qe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ve=e,_e=null,!0):!1;default:return!1}}function Ra(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ma(e){if(K){var t=_e;if(t){var n=t;if(!vc(e,t)){if(Ra(e))throw Error(N(418));t=Xt(n.nextSibling);var r=Ve;t&&vc(e,t)?vh(r,n):(e.flags=e.flags&-4097|2,K=!1,Ve=e)}}else{if(Ra(e))throw Error(N(418));e.flags=e.flags&-4097|2,K=!1,Ve=e}}}function xc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ve=e}function Ii(e){if(e!==Ve)return!1;if(!K)return xc(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ja(e.type,e.memoizedProps)),t&&(t=_e)){if(Ra(e))throw xh(),Error(N(418));for(;t;)vh(e,t),t=Xt(t.nextSibling)}if(xc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){_e=Xt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}_e=null}}else _e=Ve?Xt(e.stateNode.nextSibling):null;return!0}function xh(){for(var e=_e;e;)e=Xt(e.nextSibling)}function ir(){_e=Ve=null,K=!1}function Vl(e){rt===null?rt=[e]:rt.push(e)}var tv=Ft.ReactCurrentBatchConfig;function kr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function zi(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function wc(e){var t=e._init;return t(e._payload)}function wh(e){function t(g,d){if(e){var f=g.deletions;f===null?(g.deletions=[d],g.flags|=16):f.push(d)}}function n(g,d){if(!e)return null;for(;d!==null;)t(g,d),d=d.sibling;return null}function r(g,d){for(g=new Map;d!==null;)d.key!==null?g.set(d.key,d):g.set(d.index,d),d=d.sibling;return g}function i(g,d){return g=en(g,d),g.index=0,g.sibling=null,g}function s(g,d,f){return g.index=f,e?(f=g.alternate,f!==null?(f=f.index,f<d?(g.flags|=2,d):f):(g.flags|=2,d)):(g.flags|=1048576,d)}function o(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,d,f,v){return d===null||d.tag!==6?(d=$o(f,g.mode,v),d.return=g,d):(d=i(d,f),d.return=g,d)}function l(g,d,f,v){var S=f.type;return S===_n?c(g,d,f.props.children,v,f.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===bt&&wc(S)===d.type)?(v=i(d,f.props),v.ref=kr(g,d,f),v.return=g,v):(v=os(f.type,f.key,f.props,null,g.mode,v),v.ref=kr(g,d,f),v.return=g,v)}function u(g,d,f,v){return d===null||d.tag!==4||d.stateNode.containerInfo!==f.containerInfo||d.stateNode.implementation!==f.implementation?(d=Uo(f,g.mode,v),d.return=g,d):(d=i(d,f.children||[]),d.return=g,d)}function c(g,d,f,v,S){return d===null||d.tag!==7?(d=Sn(f,g.mode,v,S),d.return=g,d):(d=i(d,f),d.return=g,d)}function h(g,d,f){if(typeof d=="string"&&d!==""||typeof d=="number")return d=$o(""+d,g.mode,f),d.return=g,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Ni:return f=os(d.type,d.key,d.props,null,g.mode,f),f.ref=kr(g,null,d),f.return=g,f;case Fn:return d=Uo(d,g.mode,f),d.return=g,d;case bt:var v=d._init;return h(g,v(d._payload),f)}if(Lr(d)||yr(d))return d=Sn(d,g.mode,f,null),d.return=g,d;zi(g,d)}return null}function m(g,d,f,v){var S=d!==null?d.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return S!==null?null:a(g,d,""+f,v);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Ni:return f.key===S?l(g,d,f,v):null;case Fn:return f.key===S?u(g,d,f,v):null;case bt:return S=f._init,m(g,d,S(f._payload),v)}if(Lr(f)||yr(f))return S!==null?null:c(g,d,f,v,null);zi(g,f)}return null}function y(g,d,f,v,S){if(typeof v=="string"&&v!==""||typeof v=="number")return g=g.get(f)||null,a(d,g,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Ni:return g=g.get(v.key===null?f:v.key)||null,l(d,g,v,S);case Fn:return g=g.get(v.key===null?f:v.key)||null,u(d,g,v,S);case bt:var k=v._init;return y(g,d,f,k(v._payload),S)}if(Lr(v)||yr(v))return g=g.get(f)||null,c(d,g,v,S,null);zi(d,v)}return null}function x(g,d,f,v){for(var S=null,k=null,E=d,T=d=0,j=null;E!==null&&T<f.length;T++){E.index>T?(j=E,E=null):j=E.sibling;var L=m(g,E,f[T],v);if(L===null){E===null&&(E=j);break}e&&E&&L.alternate===null&&t(g,E),d=s(L,d,T),k===null?S=L:k.sibling=L,k=L,E=j}if(T===f.length)return n(g,E),K&&dn(g,T),S;if(E===null){for(;T<f.length;T++)E=h(g,f[T],v),E!==null&&(d=s(E,d,T),k===null?S=E:k.sibling=E,k=E);return K&&dn(g,T),S}for(E=r(g,E);T<f.length;T++)j=y(E,g,T,f[T],v),j!==null&&(e&&j.alternate!==null&&E.delete(j.key===null?T:j.key),d=s(j,d,T),k===null?S=j:k.sibling=j,k=j);return e&&E.forEach(function(V){return t(g,V)}),K&&dn(g,T),S}function w(g,d,f,v){var S=yr(f);if(typeof S!="function")throw Error(N(150));if(f=S.call(f),f==null)throw Error(N(151));for(var k=S=null,E=d,T=d=0,j=null,L=f.next();E!==null&&!L.done;T++,L=f.next()){E.index>T?(j=E,E=null):j=E.sibling;var V=m(g,E,L.value,v);if(V===null){E===null&&(E=j);break}e&&E&&V.alternate===null&&t(g,E),d=s(V,d,T),k===null?S=V:k.sibling=V,k=V,E=j}if(L.done)return n(g,E),K&&dn(g,T),S;if(E===null){for(;!L.done;T++,L=f.next())L=h(g,L.value,v),L!==null&&(d=s(L,d,T),k===null?S=L:k.sibling=L,k=L);return K&&dn(g,T),S}for(E=r(g,E);!L.done;T++,L=f.next())L=y(E,g,T,L.value,v),L!==null&&(e&&L.alternate!==null&&E.delete(L.key===null?T:L.key),d=s(L,d,T),k===null?S=L:k.sibling=L,k=L);return e&&E.forEach(function(b){return t(g,b)}),K&&dn(g,T),S}function P(g,d,f,v){if(typeof f=="object"&&f!==null&&f.type===_n&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Ni:e:{for(var S=f.key,k=d;k!==null;){if(k.key===S){if(S=f.type,S===_n){if(k.tag===7){n(g,k.sibling),d=i(k,f.props.children),d.return=g,g=d;break e}}else if(k.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===bt&&wc(S)===k.type){n(g,k.sibling),d=i(k,f.props),d.ref=kr(g,k,f),d.return=g,g=d;break e}n(g,k);break}else t(g,k);k=k.sibling}f.type===_n?(d=Sn(f.props.children,g.mode,v,f.key),d.return=g,g=d):(v=os(f.type,f.key,f.props,null,g.mode,v),v.ref=kr(g,d,f),v.return=g,g=v)}return o(g);case Fn:e:{for(k=f.key;d!==null;){if(d.key===k)if(d.tag===4&&d.stateNode.containerInfo===f.containerInfo&&d.stateNode.implementation===f.implementation){n(g,d.sibling),d=i(d,f.children||[]),d.return=g,g=d;break e}else{n(g,d);break}else t(g,d);d=d.sibling}d=Uo(f,g.mode,v),d.return=g,g=d}return o(g);case bt:return k=f._init,P(g,d,k(f._payload),v)}if(Lr(f))return x(g,d,f,v);if(yr(f))return w(g,d,f,v);zi(g,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,d!==null&&d.tag===6?(n(g,d.sibling),d=i(d,f),d.return=g,g=d):(n(g,d),d=$o(f,g.mode,v),d.return=g,g=d),o(g)):n(g,d)}return P}var sr=wh(!0),Sh=wh(!1),Es=ln(null),Ts=null,Un=null,Ol=null;function Il(){Ol=Un=Ts=null}function zl(e){var t=Es.current;H(Es),e._currentValue=t}function Da(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function er(e,t){Ts=e,Ol=Un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ae=!0),e.firstContext=null)}function Xe(e){var t=e._currentValue;if(Ol!==e)if(e={context:e,memoizedValue:t,next:null},Un===null){if(Ts===null)throw Error(N(308));Un=e,Ts.dependencies={lanes:0,firstContext:e}}else Un=Un.next=e;return t}var yn=null;function bl(e){yn===null?yn=[e]:yn.push(e)}function kh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,bl(t)):(n.next=i.next,i.next=n),t.interleaved=n,Lt(e,r)}function Lt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Bt=!1;function Bl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ch(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Zt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Lt(e,n)}return i=r.interleaved,i===null?(t.next=t,bl(r)):(t.next=i.next,i.next=t),r.interleaved=t,Lt(e,n)}function es(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Tl(e,n)}}function Sc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function js(e,t,n,r){var i=e.updateQueue;Bt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;o=0,c=u=l=null,a=s;do{var m=a.lane,y=a.eventTime;if((r&m)===m){c!==null&&(c=c.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,w=a;switch(m=t,y=n,w.tag){case 1:if(x=w.payload,typeof x=="function"){h=x.call(y,h,m);break e}h=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=w.payload,m=typeof x=="function"?x.call(y,h,m):x,m==null)break e;h=J({},h,m);break e;case 2:Bt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,m=i.effects,m===null?i.effects=[a]:m.push(a))}else y={eventTime:y,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=y,l=h):c=c.next=y,o|=m;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;m=a,a=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);if(c===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);Tn|=o,e.lanes=o,e.memoizedState=h}}function kc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(N(191,i));i.call(r)}}}var xi={},pt=ln(xi),si=ln(xi),oi=ln(xi);function vn(e){if(e===xi)throw Error(N(174));return e}function $l(e,t){switch($(oi,t),$(si,e),$(pt,xi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ha(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ha(t,e)}H(pt),$(pt,t)}function or(){H(pt),H(si),H(oi)}function Ph(e){vn(oi.current);var t=vn(pt.current),n=ha(t,e.type);t!==n&&($(si,e),$(pt,n))}function Ul(e){si.current===e&&(H(pt),H(si))}var Y=ln(0);function Ns(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Vo=[];function Hl(){for(var e=0;e<Vo.length;e++)Vo[e]._workInProgressVersionPrimary=null;Vo.length=0}var ts=Ft.ReactCurrentDispatcher,Oo=Ft.ReactCurrentBatchConfig,En=0,Z=null,ae=null,fe=null,As=!1,br=!1,ai=0,nv=0;function ye(){throw Error(N(321))}function Wl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!at(e[n],t[n]))return!1;return!0}function Gl(e,t,n,r,i,s){if(En=s,Z=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ts.current=e===null||e.memoizedState===null?ov:av,e=n(r,i),br){s=0;do{if(br=!1,ai=0,25<=s)throw Error(N(301));s+=1,fe=ae=null,t.updateQueue=null,ts.current=lv,e=n(r,i)}while(br)}if(ts.current=Ls,t=ae!==null&&ae.next!==null,En=0,fe=ae=Z=null,As=!1,t)throw Error(N(300));return e}function Kl(){var e=ai!==0;return ai=0,e}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?Z.memoizedState=fe=e:fe=fe.next=e,fe}function Ze(){if(ae===null){var e=Z.alternate;e=e!==null?e.memoizedState:null}else e=ae.next;var t=fe===null?Z.memoizedState:fe.next;if(t!==null)fe=t,ae=e;else{if(e===null)throw Error(N(310));ae=e,e={memoizedState:ae.memoizedState,baseState:ae.baseState,baseQueue:ae.baseQueue,queue:ae.queue,next:null},fe===null?Z.memoizedState=fe=e:fe=fe.next=e}return fe}function li(e,t){return typeof t=="function"?t(e):t}function Io(e){var t=Ze(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=ae,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((En&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,Z.lanes|=c,Tn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,at(r,t.memoizedState)||(Ae=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,Z.lanes|=s,Tn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function zo(e){var t=Ze(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);at(s,t.memoizedState)||(Ae=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Eh(){}function Th(e,t){var n=Z,r=Ze(),i=t(),s=!at(r.memoizedState,i);if(s&&(r.memoizedState=i,Ae=!0),r=r.queue,Ql(Ah.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||fe!==null&&fe.memoizedState.tag&1){if(n.flags|=2048,ui(9,Nh.bind(null,n,r,i,t),void 0,null),de===null)throw Error(N(349));En&30||jh(n,t,i)}return i}function jh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Nh(e,t,n,r){t.value=n,t.getSnapshot=r,Lh(t)&&Rh(e)}function Ah(e,t,n){return n(function(){Lh(t)&&Rh(e)})}function Lh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!at(e,n)}catch{return!0}}function Rh(e){var t=Lt(e,1);t!==null&&ot(t,e,1,-1)}function Cc(e){var t=ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:e},t.queue=e,e=e.dispatch=sv.bind(null,Z,e),[t.memoizedState,e]}function ui(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Mh(){return Ze().memoizedState}function ns(e,t,n,r){var i=ft();Z.flags|=e,i.memoizedState=ui(1|t,n,void 0,r===void 0?null:r)}function qs(e,t,n,r){var i=Ze();r=r===void 0?null:r;var s=void 0;if(ae!==null){var o=ae.memoizedState;if(s=o.destroy,r!==null&&Wl(r,o.deps)){i.memoizedState=ui(t,n,s,r);return}}Z.flags|=e,i.memoizedState=ui(1|t,n,s,r)}function Pc(e,t){return ns(8390656,8,e,t)}function Ql(e,t){return qs(2048,8,e,t)}function Dh(e,t){return qs(4,2,e,t)}function Fh(e,t){return qs(4,4,e,t)}function _h(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vh(e,t,n){return n=n!=null?n.concat([e]):null,qs(4,4,_h.bind(null,t,e),n)}function Yl(){}function Oh(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ih(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zh(e,t,n){return En&21?(at(n,t)||(n=Hd(),Z.lanes|=n,Tn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ae=!0),e.memoizedState=n)}function rv(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=Oo.transition;Oo.transition={};try{e(!1),t()}finally{B=n,Oo.transition=r}}function bh(){return Ze().memoizedState}function iv(e,t,n){var r=Jt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Bh(e))$h(t,n);else if(n=kh(e,t,n,r),n!==null){var i=Ee();ot(n,e,r,i),Uh(n,t,r)}}function sv(e,t,n){var r=Jt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bh(e))$h(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,at(a,o)){var l=t.interleaved;l===null?(i.next=i,bl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=kh(e,t,i,r),n!==null&&(i=Ee(),ot(n,e,r,i),Uh(n,t,r))}}function Bh(e){var t=e.alternate;return e===Z||t!==null&&t===Z}function $h(e,t){br=As=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Uh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Tl(e,n)}}var Ls={readContext:Xe,useCallback:ye,useContext:ye,useEffect:ye,useImperativeHandle:ye,useInsertionEffect:ye,useLayoutEffect:ye,useMemo:ye,useReducer:ye,useRef:ye,useState:ye,useDebugValue:ye,useDeferredValue:ye,useTransition:ye,useMutableSource:ye,useSyncExternalStore:ye,useId:ye,unstable_isNewReconciler:!1},ov={readContext:Xe,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:Xe,useEffect:Pc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ns(4194308,4,_h.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ns(4194308,4,e,t)},useInsertionEffect:function(e,t){return ns(4,2,e,t)},useMemo:function(e,t){var n=ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=iv.bind(null,Z,e),[r.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:Cc,useDebugValue:Yl,useDeferredValue:function(e){return ft().memoizedState=e},useTransition:function(){var e=Cc(!1),t=e[0];return e=rv.bind(null,e[1]),ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Z,i=ft();if(K){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),de===null)throw Error(N(349));En&30||jh(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Pc(Ah.bind(null,r,s,e),[e]),r.flags|=2048,ui(9,Nh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ft(),t=de.identifierPrefix;if(K){var n=kt,r=St;n=(r&~(1<<32-st(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ai++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=nv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},av={readContext:Xe,useCallback:Oh,useContext:Xe,useEffect:Ql,useImperativeHandle:Vh,useInsertionEffect:Dh,useLayoutEffect:Fh,useMemo:Ih,useReducer:Io,useRef:Mh,useState:function(){return Io(li)},useDebugValue:Yl,useDeferredValue:function(e){var t=Ze();return zh(t,ae.memoizedState,e)},useTransition:function(){var e=Io(li)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Eh,useSyncExternalStore:Th,useId:bh,unstable_isNewReconciler:!1},lv={readContext:Xe,useCallback:Oh,useContext:Xe,useEffect:Ql,useImperativeHandle:Vh,useInsertionEffect:Dh,useLayoutEffect:Fh,useMemo:Ih,useReducer:zo,useRef:Mh,useState:function(){return zo(li)},useDebugValue:Yl,useDeferredValue:function(e){var t=Ze();return ae===null?t.memoizedState=e:zh(t,ae.memoizedState,e)},useTransition:function(){var e=zo(li)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Eh,useSyncExternalStore:Th,useId:bh,unstable_isNewReconciler:!1};function et(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Fa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Js={isMounted:function(e){return(e=e._reactInternals)?An(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ee(),i=Jt(e),s=Pt(r,i);s.payload=t,n!=null&&(s.callback=n),t=Zt(e,s,i),t!==null&&(ot(t,e,i,r),es(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ee(),i=Jt(e),s=Pt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Zt(e,s,i),t!==null&&(ot(t,e,i,r),es(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ee(),r=Jt(e),i=Pt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Zt(e,i,r),t!==null&&(ot(t,e,r,n),es(t,e,r))}};function Ec(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!ti(n,r)||!ti(i,s):!0}function Hh(e,t,n){var r=!1,i=rn,s=t.contextType;return typeof s=="object"&&s!==null?s=Xe(s):(i=Re(t)?Cn:Se.current,r=t.contextTypes,s=(r=r!=null)?rr(e,i):rn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Js,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Tc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Js.enqueueReplaceState(t,t.state,null)}function _a(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Bl(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Xe(s):(s=Re(t)?Cn:Se.current,i.context=rr(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Fa(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Js.enqueueReplaceState(i,i.state,null),js(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function ar(e,t){try{var n="",r=t;do n+=Vg(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function bo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Va(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var uv=typeof WeakMap=="function"?WeakMap:Map;function Wh(e,t,n){n=Pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ms||(Ms=!0,Ga=r),Va(e,t)},n}function Gh(e,t,n){n=Pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Va(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Va(e,t),typeof r!="function"&&(qt===null?qt=new Set([this]):qt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function jc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new uv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Cv.bind(null,e,t,n),t.then(e,e))}function Nc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ac(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Pt(-1,1),t.tag=2,Zt(n,t,1))),n.lanes|=1),e)}var cv=Ft.ReactCurrentOwner,Ae=!1;function Pe(e,t,n,r){t.child=e===null?Sh(t,null,n,r):sr(t,e.child,n,r)}function Lc(e,t,n,r,i){n=n.render;var s=t.ref;return er(t,i),r=Gl(e,t,n,r,s,i),n=Kl(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Rt(e,t,i)):(K&&n&&Fl(t),t.flags|=1,Pe(e,t,r,i),t.child)}function Rc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!ru(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Kh(e,t,s,r,i)):(e=os(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:ti,n(o,r)&&e.ref===t.ref)return Rt(e,t,i)}return t.flags|=1,e=en(s,r),e.ref=t.ref,e.return=t,t.child=e}function Kh(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(ti(s,r)&&e.ref===t.ref)if(Ae=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ae=!0);else return t.lanes=e.lanes,Rt(e,t,i)}return Oa(e,t,n,r,i)}function Qh(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(Wn,Fe),Fe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(Wn,Fe),Fe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,$(Wn,Fe),Fe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,$(Wn,Fe),Fe|=r;return Pe(e,t,i,n),t.child}function Yh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Oa(e,t,n,r,i){var s=Re(n)?Cn:Se.current;return s=rr(t,s),er(t,i),n=Gl(e,t,n,r,s,i),r=Kl(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Rt(e,t,i)):(K&&r&&Fl(t),t.flags|=1,Pe(e,t,n,i),t.child)}function Mc(e,t,n,r,i){if(Re(n)){var s=!0;ks(t)}else s=!1;if(er(t,i),t.stateNode===null)rs(e,t),Hh(t,n,r),_a(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Xe(u):(u=Re(n)?Cn:Se.current,u=rr(t,u));var c=n.getDerivedStateFromProps,h=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Tc(t,o,r,u),Bt=!1;var m=t.memoizedState;o.state=m,js(t,r,o,i),l=t.memoizedState,a!==r||m!==l||Le.current||Bt?(typeof c=="function"&&(Fa(t,n,c,r),l=t.memoizedState),(a=Bt||Ec(t,n,a,r,m,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Ch(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:et(t.type,a),o.props=u,h=t.pendingProps,m=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=Xe(l):(l=Re(n)?Cn:Se.current,l=rr(t,l));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||m!==l)&&Tc(t,o,r,l),Bt=!1,m=t.memoizedState,o.state=m,js(t,r,o,i);var x=t.memoizedState;a!==h||m!==x||Le.current||Bt?(typeof y=="function"&&(Fa(t,n,y,r),x=t.memoizedState),(u=Bt||Ec(t,n,u,r,m,x,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,x,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,x,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),o.props=r,o.state=x,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Ia(e,t,n,r,s,i)}function Ia(e,t,n,r,i,s){Yh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&yc(t,n,!1),Rt(e,t,s);r=t.stateNode,cv.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=sr(t,e.child,null,s),t.child=sr(t,null,a,s)):Pe(e,t,a,s),t.memoizedState=r.state,i&&yc(t,n,!0),t.child}function Xh(e){var t=e.stateNode;t.pendingContext?gc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&gc(e,t.context,!1),$l(e,t.containerInfo)}function Dc(e,t,n,r,i){return ir(),Vl(i),t.flags|=256,Pe(e,t,n,r),t.child}var za={dehydrated:null,treeContext:null,retryLane:0};function ba(e){return{baseLanes:e,cachePool:null,transitions:null}}function Zh(e,t,n){var r=t.pendingProps,i=Y.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),$(Y,i&1),e===null)return Ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=no(o,r,0,null),e=Sn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=ba(n),t.memoizedState=za,e):Xl(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return fv(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=en(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=en(a,s):(s=Sn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?ba(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=za,r}return s=e.child,e=s.sibling,r=en(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xl(e,t){return t=no({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bi(e,t,n,r){return r!==null&&Vl(r),sr(t,e.child,null,n),e=Xl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function fv(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=bo(Error(N(422))),bi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=no({mode:"visible",children:r.children},i,0,null),s=Sn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&sr(t,e.child,null,o),t.child.memoizedState=ba(o),t.memoizedState=za,s);if(!(t.mode&1))return bi(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(N(419)),r=bo(s,r,void 0),bi(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ae||a){if(r=de,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Lt(e,i),ot(r,e,i,-1))}return nu(),r=bo(Error(N(421))),bi(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Pv.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,_e=Xt(i.nextSibling),Ve=t,K=!0,rt=null,e!==null&&(Ge[Ke++]=St,Ge[Ke++]=kt,Ge[Ke++]=Pn,St=e.id,kt=e.overflow,Pn=t),t=Xl(t,r.children),t.flags|=4096,t)}function Fc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Da(e.return,t,n)}function Bo(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function qh(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(Pe(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Fc(e,n,t);else if(e.tag===19)Fc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(Y,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Ns(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Bo(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Ns(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Bo(t,!0,n,null,s);break;case"together":Bo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function rs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Rt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=en(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=en(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function dv(e,t,n){switch(t.tag){case 3:Xh(t),ir();break;case 5:Ph(t);break;case 1:Re(t.type)&&ks(t);break;case 4:$l(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;$(Es,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?Zh(e,t,n):($(Y,Y.current&1),e=Rt(e,t,n),e!==null?e.sibling:null);$(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qh(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,Qh(e,t,n)}return Rt(e,t,n)}var Jh,Ba,ep,tp;Jh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ba=function(){};ep=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,vn(pt.current);var s=null;switch(n){case"input":i=ua(e,i),r=ua(e,r),s=[];break;case"select":i=J({},i,{value:void 0}),r=J({},r,{value:void 0}),s=[];break;case"textarea":i=da(e,i),r=da(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ws)}pa(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Qr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Qr.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&U("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};tp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Cr(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function hv(e,t,n){var r=t.pendingProps;switch(_l(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ve(t),null;case 1:return Re(t.type)&&Ss(),ve(t),null;case 3:return r=t.stateNode,or(),H(Le),H(Se),Hl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ii(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,rt!==null&&(Ya(rt),rt=null))),Ba(e,t),ve(t),null;case 5:Ul(t);var i=vn(oi.current);if(n=t.type,e!==null&&t.stateNode!=null)ep(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return ve(t),null}if(e=vn(pt.current),Ii(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[dt]=t,r[ii]=s,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<Mr.length;i++)U(Mr[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Uu(r,s),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},U("invalid",r);break;case"textarea":Wu(r,s),U("invalid",r)}pa(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Oi(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Oi(r.textContent,a,e),i=["children",""+a]):Qr.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&U("scroll",r)}switch(n){case"input":Ai(r),Hu(r,s,!0);break;case"textarea":Ai(r),Gu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ws)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ad(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[dt]=t,e[ii]=r,Jh(e,t,!1,!1),t.stateNode=e;e:{switch(o=ma(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<Mr.length;i++)U(Mr[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":Uu(e,r),i=ua(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=J({},r,{value:void 0}),U("invalid",e);break;case"textarea":Wu(e,r),i=da(e,r),U("invalid",e);break;default:i=r}pa(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Md(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Ld(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Yr(e,l):typeof l=="number"&&Yr(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Qr.hasOwnProperty(s)?l!=null&&s==="onScroll"&&U("scroll",e):l!=null&&wl(e,s,l,o))}switch(n){case"input":Ai(e),Hu(e,r,!1);break;case"textarea":Ai(e),Gu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+nn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Xn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Xn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ws)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ve(t),null;case 6:if(e&&t.stateNode!=null)tp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=vn(oi.current),vn(pt.current),Ii(t)){if(r=t.stateNode,n=t.memoizedProps,r[dt]=t,(s=r.nodeValue!==n)&&(e=Ve,e!==null))switch(e.tag){case 3:Oi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[dt]=t,t.stateNode=r}return ve(t),null;case 13:if(H(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&_e!==null&&t.mode&1&&!(t.flags&128))xh(),ir(),t.flags|=98560,s=!1;else if(s=Ii(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(N(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(N(317));s[dt]=t}else ir(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ve(t),s=!1}else rt!==null&&(Ya(rt),rt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?le===0&&(le=3):nu())),t.updateQueue!==null&&(t.flags|=4),ve(t),null);case 4:return or(),Ba(e,t),e===null&&ni(t.stateNode.containerInfo),ve(t),null;case 10:return zl(t.type._context),ve(t),null;case 17:return Re(t.type)&&Ss(),ve(t),null;case 19:if(H(Y),s=t.memoizedState,s===null)return ve(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)Cr(s,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ns(e),o!==null){for(t.flags|=128,Cr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(Y,Y.current&1|2),t.child}e=e.sibling}s.tail!==null&&ne()>lr&&(t.flags|=128,r=!0,Cr(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ns(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Cr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!K)return ve(t),null}else 2*ne()-s.renderingStartTime>lr&&n!==1073741824&&(t.flags|=128,r=!0,Cr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ne(),t.sibling=null,n=Y.current,$(Y,r?n&1|2:n&1),t):(ve(t),null);case 22:case 23:return tu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Fe&1073741824&&(ve(t),t.subtreeFlags&6&&(t.flags|=8192)):ve(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function pv(e,t){switch(_l(t),t.tag){case 1:return Re(t.type)&&Ss(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return or(),H(Le),H(Se),Hl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ul(t),null;case 13:if(H(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));ir()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(Y),null;case 4:return or(),null;case 10:return zl(t.type._context),null;case 22:case 23:return tu(),null;case 24:return null;default:return null}}var Bi=!1,we=!1,mv=typeof WeakSet=="function"?WeakSet:Set,R=null;function Hn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function $a(e,t,n){try{n()}catch(r){ee(e,t,r)}}var _c=!1;function gv(e,t){if(Ea=ys,e=oh(),Dl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,h=e,m=null;t:for(;;){for(var y;h!==n||i!==0&&h.nodeType!==3||(a=o+i),h!==s||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(y=h.firstChild)!==null;)m=h,h=y;for(;;){if(h===e)break t;if(m===n&&++u===i&&(a=o),m===s&&++c===r&&(l=o),(y=h.nextSibling)!==null)break;h=m,m=h.parentNode}h=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ta={focusedElem:e,selectionRange:n},ys=!1,R=t;R!==null;)if(t=R,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,R=e;else for(;R!==null;){t=R;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var w=x.memoizedProps,P=x.memoizedState,g=t.stateNode,d=g.getSnapshotBeforeUpdate(t.elementType===t.type?w:et(t.type,w),P);g.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(v){ee(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,R=e;break}R=t.return}return x=_c,_c=!1,x}function Br(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&$a(t,n,s)}i=i.next}while(i!==r)}}function eo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ua(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function np(e){var t=e.alternate;t!==null&&(e.alternate=null,np(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[ii],delete t[Aa],delete t[qy],delete t[Jy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function rp(e){return e.tag===5||e.tag===3||e.tag===4}function Vc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ha(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ws));else if(r!==4&&(e=e.child,e!==null))for(Ha(e,t,n),e=e.sibling;e!==null;)Ha(e,t,n),e=e.sibling}function Wa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Wa(e,t,n),e=e.sibling;e!==null;)Wa(e,t,n),e=e.sibling}var pe=null,nt=!1;function It(e,t,n){for(n=n.child;n!==null;)ip(e,t,n),n=n.sibling}function ip(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(Gs,n)}catch{}switch(n.tag){case 5:we||Hn(n,t);case 6:var r=pe,i=nt;pe=null,It(e,t,n),pe=r,nt=i,pe!==null&&(nt?(e=pe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):pe.removeChild(n.stateNode));break;case 18:pe!==null&&(nt?(e=pe,n=n.stateNode,e.nodeType===8?Fo(e.parentNode,n):e.nodeType===1&&Fo(e,n),Jr(e)):Fo(pe,n.stateNode));break;case 4:r=pe,i=nt,pe=n.stateNode.containerInfo,nt=!0,It(e,t,n),pe=r,nt=i;break;case 0:case 11:case 14:case 15:if(!we&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&$a(n,t,o),i=i.next}while(i!==r)}It(e,t,n);break;case 1:if(!we&&(Hn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ee(n,t,a)}It(e,t,n);break;case 21:It(e,t,n);break;case 22:n.mode&1?(we=(r=we)||n.memoizedState!==null,It(e,t,n),we=r):It(e,t,n);break;default:It(e,t,n)}}function Oc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new mv),t.forEach(function(r){var i=Ev.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function qe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:pe=a.stateNode,nt=!1;break e;case 3:pe=a.stateNode.containerInfo,nt=!0;break e;case 4:pe=a.stateNode.containerInfo,nt=!0;break e}a=a.return}if(pe===null)throw Error(N(160));ip(s,o,i),pe=null,nt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ee(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sp(t,e),t=t.sibling}function sp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(qe(t,e),ut(e),r&4){try{Br(3,e,e.return),eo(3,e)}catch(w){ee(e,e.return,w)}try{Br(5,e,e.return)}catch(w){ee(e,e.return,w)}}break;case 1:qe(t,e),ut(e),r&512&&n!==null&&Hn(n,n.return);break;case 5:if(qe(t,e),ut(e),r&512&&n!==null&&Hn(n,n.return),e.flags&32){var i=e.stateNode;try{Yr(i,"")}catch(w){ee(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&jd(i,s),ma(a,o);var u=ma(a,s);for(o=0;o<l.length;o+=2){var c=l[o],h=l[o+1];c==="style"?Md(i,h):c==="dangerouslySetInnerHTML"?Ld(i,h):c==="children"?Yr(i,h):wl(i,c,h,u)}switch(a){case"input":ca(i,s);break;case"textarea":Nd(i,s);break;case"select":var m=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?Xn(i,!!s.multiple,y,!1):m!==!!s.multiple&&(s.defaultValue!=null?Xn(i,!!s.multiple,s.defaultValue,!0):Xn(i,!!s.multiple,s.multiple?[]:"",!1))}i[ii]=s}catch(w){ee(e,e.return,w)}}break;case 6:if(qe(t,e),ut(e),r&4){if(e.stateNode===null)throw Error(N(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(w){ee(e,e.return,w)}}break;case 3:if(qe(t,e),ut(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Jr(t.containerInfo)}catch(w){ee(e,e.return,w)}break;case 4:qe(t,e),ut(e);break;case 13:qe(t,e),ut(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Jl=ne())),r&4&&Oc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(we=(u=we)||c,qe(t,e),we=u):qe(t,e),ut(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(R=e,c=e.child;c!==null;){for(h=R=c;R!==null;){switch(m=R,y=m.child,m.tag){case 0:case 11:case 14:case 15:Br(4,m,m.return);break;case 1:Hn(m,m.return);var x=m.stateNode;if(typeof x.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(w){ee(r,n,w)}}break;case 5:Hn(m,m.return);break;case 22:if(m.memoizedState!==null){zc(h);continue}}y!==null?(y.return=m,R=y):zc(h)}c=c.sibling}e:for(c=null,h=e;;){if(h.tag===5){if(c===null){c=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Rd("display",o))}catch(w){ee(e,e.return,w)}}}else if(h.tag===6){if(c===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(w){ee(e,e.return,w)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;c===h&&(c=null),h=h.return}c===h&&(c=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:qe(t,e),ut(e),r&4&&Oc(e);break;case 21:break;default:qe(t,e),ut(e)}}function ut(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(rp(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Yr(i,""),r.flags&=-33);var s=Vc(e);Wa(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Vc(e);Ha(e,a,o);break;default:throw Error(N(161))}}catch(l){ee(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function yv(e,t,n){R=e,op(e)}function op(e,t,n){for(var r=(e.mode&1)!==0;R!==null;){var i=R,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Bi;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||we;a=Bi;var u=we;if(Bi=o,(we=l)&&!u)for(R=i;R!==null;)o=R,l=o.child,o.tag===22&&o.memoizedState!==null?bc(i):l!==null?(l.return=o,R=l):bc(i);for(;s!==null;)R=s,op(s),s=s.sibling;R=i,Bi=a,we=u}Ic(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,R=s):Ic(e)}}function Ic(e){for(;R!==null;){var t=R;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:we||eo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!we)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:et(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&kc(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}kc(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var h=c.dehydrated;h!==null&&Jr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}we||t.flags&512&&Ua(t)}catch(m){ee(t,t.return,m)}}if(t===e){R=null;break}if(n=t.sibling,n!==null){n.return=t.return,R=n;break}R=t.return}}function zc(e){for(;R!==null;){var t=R;if(t===e){R=null;break}var n=t.sibling;if(n!==null){n.return=t.return,R=n;break}R=t.return}}function bc(e){for(;R!==null;){var t=R;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{eo(4,t)}catch(l){ee(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ee(t,i,l)}}var s=t.return;try{Ua(t)}catch(l){ee(t,s,l)}break;case 5:var o=t.return;try{Ua(t)}catch(l){ee(t,o,l)}}}catch(l){ee(t,t.return,l)}if(t===e){R=null;break}var a=t.sibling;if(a!==null){a.return=t.return,R=a;break}R=t.return}}var vv=Math.ceil,Rs=Ft.ReactCurrentDispatcher,Zl=Ft.ReactCurrentOwner,Ye=Ft.ReactCurrentBatchConfig,z=0,de=null,se=null,me=0,Fe=0,Wn=ln(0),le=0,ci=null,Tn=0,to=0,ql=0,$r=null,Ne=null,Jl=0,lr=1/0,xt=null,Ms=!1,Ga=null,qt=null,$i=!1,Gt=null,Ds=0,Ur=0,Ka=null,is=-1,ss=0;function Ee(){return z&6?ne():is!==-1?is:is=ne()}function Jt(e){return e.mode&1?z&2&&me!==0?me&-me:tv.transition!==null?(ss===0&&(ss=Hd()),ss):(e=B,e!==0||(e=window.event,e=e===void 0?16:Zd(e.type)),e):1}function ot(e,t,n,r){if(50<Ur)throw Ur=0,Ka=null,Error(N(185));gi(e,n,r),(!(z&2)||e!==de)&&(e===de&&(!(z&2)&&(to|=n),le===4&&Ht(e,me)),Me(e,r),n===1&&z===0&&!(t.mode&1)&&(lr=ne()+500,Zs&&un()))}function Me(e,t){var n=e.callbackNode;ty(e,t);var r=gs(e,e===de?me:0);if(r===0)n!==null&&Yu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Yu(n),t===1)e.tag===0?ev(Bc.bind(null,e)):gh(Bc.bind(null,e)),Xy(function(){!(z&6)&&un()}),n=null;else{switch(Wd(r)){case 1:n=El;break;case 4:n=$d;break;case 16:n=ms;break;case 536870912:n=Ud;break;default:n=ms}n=pp(n,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ap(e,t){if(is=-1,ss=0,z&6)throw Error(N(327));var n=e.callbackNode;if(tr()&&e.callbackNode!==n)return null;var r=gs(e,e===de?me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Fs(e,r);else{t=r;var i=z;z|=2;var s=up();(de!==e||me!==t)&&(xt=null,lr=ne()+500,wn(e,t));do try{Sv();break}catch(a){lp(e,a)}while(!0);Il(),Rs.current=s,z=i,se!==null?t=0:(de=null,me=0,t=le)}if(t!==0){if(t===2&&(i=wa(e),i!==0&&(r=i,t=Qa(e,i))),t===1)throw n=ci,wn(e,0),Ht(e,r),Me(e,ne()),n;if(t===6)Ht(e,r);else{if(i=e.current.alternate,!(r&30)&&!xv(i)&&(t=Fs(e,r),t===2&&(s=wa(e),s!==0&&(r=s,t=Qa(e,s))),t===1))throw n=ci,wn(e,0),Ht(e,r),Me(e,ne()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:hn(e,Ne,xt);break;case 3:if(Ht(e,r),(r&130023424)===r&&(t=Jl+500-ne(),10<t)){if(gs(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ee(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Na(hn.bind(null,e,Ne,xt),t);break}hn(e,Ne,xt);break;case 4:if(Ht(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-st(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=ne()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*vv(r/1960))-r,10<r){e.timeoutHandle=Na(hn.bind(null,e,Ne,xt),r);break}hn(e,Ne,xt);break;case 5:hn(e,Ne,xt);break;default:throw Error(N(329))}}}return Me(e,ne()),e.callbackNode===n?ap.bind(null,e):null}function Qa(e,t){var n=$r;return e.current.memoizedState.isDehydrated&&(wn(e,t).flags|=256),e=Fs(e,t),e!==2&&(t=Ne,Ne=n,t!==null&&Ya(t)),e}function Ya(e){Ne===null?Ne=e:Ne.push.apply(Ne,e)}function xv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!at(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ht(e,t){for(t&=~ql,t&=~to,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function Bc(e){if(z&6)throw Error(N(327));tr();var t=gs(e,0);if(!(t&1))return Me(e,ne()),null;var n=Fs(e,t);if(e.tag!==0&&n===2){var r=wa(e);r!==0&&(t=r,n=Qa(e,r))}if(n===1)throw n=ci,wn(e,0),Ht(e,t),Me(e,ne()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,hn(e,Ne,xt),Me(e,ne()),null}function eu(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(lr=ne()+500,Zs&&un())}}function jn(e){Gt!==null&&Gt.tag===0&&!(z&6)&&tr();var t=z;z|=1;var n=Ye.transition,r=B;try{if(Ye.transition=null,B=1,e)return e()}finally{B=r,Ye.transition=n,z=t,!(z&6)&&un()}}function tu(){Fe=Wn.current,H(Wn)}function wn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Yy(n)),se!==null)for(n=se.return;n!==null;){var r=n;switch(_l(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ss();break;case 3:or(),H(Le),H(Se),Hl();break;case 5:Ul(r);break;case 4:or();break;case 13:H(Y);break;case 19:H(Y);break;case 10:zl(r.type._context);break;case 22:case 23:tu()}n=n.return}if(de=e,se=e=en(e.current,null),me=Fe=t,le=0,ci=null,ql=to=Tn=0,Ne=$r=null,yn!==null){for(t=0;t<yn.length;t++)if(n=yn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}yn=null}return e}function lp(e,t){do{var n=se;try{if(Il(),ts.current=Ls,As){for(var r=Z.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}As=!1}if(En=0,fe=ae=Z=null,br=!1,ai=0,Zl.current=null,n===null||n.return===null){le=1,ci=t,se=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=me,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,h=c.tag;if(!(c.mode&1)&&(h===0||h===11||h===15)){var m=c.alternate;m?(c.updateQueue=m.updateQueue,c.memoizedState=m.memoizedState,c.lanes=m.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=Nc(o);if(y!==null){y.flags&=-257,Ac(y,o,a,s,t),y.mode&1&&jc(s,u,t),t=y,l=u;var x=t.updateQueue;if(x===null){var w=new Set;w.add(l),t.updateQueue=w}else x.add(l);break e}else{if(!(t&1)){jc(s,u,t),nu();break e}l=Error(N(426))}}else if(K&&a.mode&1){var P=Nc(o);if(P!==null){!(P.flags&65536)&&(P.flags|=256),Ac(P,o,a,s,t),Vl(ar(l,a));break e}}s=l=ar(l,a),le!==4&&(le=2),$r===null?$r=[s]:$r.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var g=Wh(s,l,t);Sc(s,g);break e;case 1:a=l;var d=s.type,f=s.stateNode;if(!(s.flags&128)&&(typeof d.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(qt===null||!qt.has(f)))){s.flags|=65536,t&=-t,s.lanes|=t;var v=Gh(s,a,t);Sc(s,v);break e}}s=s.return}while(s!==null)}fp(n)}catch(S){t=S,se===n&&n!==null&&(se=n=n.return);continue}break}while(!0)}function up(){var e=Rs.current;return Rs.current=Ls,e===null?Ls:e}function nu(){(le===0||le===3||le===2)&&(le=4),de===null||!(Tn&268435455)&&!(to&268435455)||Ht(de,me)}function Fs(e,t){var n=z;z|=2;var r=up();(de!==e||me!==t)&&(xt=null,wn(e,t));do try{wv();break}catch(i){lp(e,i)}while(!0);if(Il(),z=n,Rs.current=r,se!==null)throw Error(N(261));return de=null,me=0,le}function wv(){for(;se!==null;)cp(se)}function Sv(){for(;se!==null&&!Gg();)cp(se)}function cp(e){var t=hp(e.alternate,e,Fe);e.memoizedProps=e.pendingProps,t===null?fp(e):se=t,Zl.current=null}function fp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=pv(n,t),n!==null){n.flags&=32767,se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,se=null;return}}else if(n=hv(n,t,Fe),n!==null){se=n;return}if(t=t.sibling,t!==null){se=t;return}se=t=e}while(t!==null);le===0&&(le=5)}function hn(e,t,n){var r=B,i=Ye.transition;try{Ye.transition=null,B=1,kv(e,t,n,r)}finally{Ye.transition=i,B=r}return null}function kv(e,t,n,r){do tr();while(Gt!==null);if(z&6)throw Error(N(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(ny(e,s),e===de&&(se=de=null,me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||$i||($i=!0,pp(ms,function(){return tr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Ye.transition,Ye.transition=null;var o=B;B=1;var a=z;z|=4,Zl.current=null,gv(e,n),sp(n,e),$y(Ta),ys=!!Ea,Ta=Ea=null,e.current=n,yv(n),Kg(),z=a,B=o,Ye.transition=s}else e.current=n;if($i&&($i=!1,Gt=e,Ds=i),s=e.pendingLanes,s===0&&(qt=null),Xg(n.stateNode),Me(e,ne()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ms)throw Ms=!1,e=Ga,Ga=null,e;return Ds&1&&e.tag!==0&&tr(),s=e.pendingLanes,s&1?e===Ka?Ur++:(Ur=0,Ka=e):Ur=0,un(),null}function tr(){if(Gt!==null){var e=Wd(Ds),t=Ye.transition,n=B;try{if(Ye.transition=null,B=16>e?16:e,Gt===null)var r=!1;else{if(e=Gt,Gt=null,Ds=0,z&6)throw Error(N(331));var i=z;for(z|=4,R=e.current;R!==null;){var s=R,o=s.child;if(R.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(R=u;R!==null;){var c=R;switch(c.tag){case 0:case 11:case 15:Br(8,c,s)}var h=c.child;if(h!==null)h.return=c,R=h;else for(;R!==null;){c=R;var m=c.sibling,y=c.return;if(np(c),c===u){R=null;break}if(m!==null){m.return=y,R=m;break}R=y}}}var x=s.alternate;if(x!==null){var w=x.child;if(w!==null){x.child=null;do{var P=w.sibling;w.sibling=null,w=P}while(w!==null)}}R=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,R=o;else e:for(;R!==null;){if(s=R,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Br(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,R=g;break e}R=s.return}}var d=e.current;for(R=d;R!==null;){o=R;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,R=f;else e:for(o=d;R!==null;){if(a=R,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:eo(9,a)}}catch(S){ee(a,a.return,S)}if(a===o){R=null;break e}var v=a.sibling;if(v!==null){v.return=a.return,R=v;break e}R=a.return}}if(z=i,un(),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(Gs,e)}catch{}r=!0}return r}finally{B=n,Ye.transition=t}}return!1}function $c(e,t,n){t=ar(n,t),t=Wh(e,t,1),e=Zt(e,t,1),t=Ee(),e!==null&&(gi(e,1,t),Me(e,t))}function ee(e,t,n){if(e.tag===3)$c(e,e,n);else for(;t!==null;){if(t.tag===3){$c(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(qt===null||!qt.has(r))){e=ar(n,e),e=Gh(t,e,1),t=Zt(t,e,1),e=Ee(),t!==null&&(gi(t,1,e),Me(t,e));break}}t=t.return}}function Cv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ee(),e.pingedLanes|=e.suspendedLanes&n,de===e&&(me&n)===n&&(le===4||le===3&&(me&130023424)===me&&500>ne()-Jl?wn(e,0):ql|=n),Me(e,t)}function dp(e,t){t===0&&(e.mode&1?(t=Mi,Mi<<=1,!(Mi&130023424)&&(Mi=4194304)):t=1);var n=Ee();e=Lt(e,t),e!==null&&(gi(e,t,n),Me(e,n))}function Pv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),dp(e,n)}function Ev(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),dp(e,n)}var hp;hp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Le.current)Ae=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ae=!1,dv(e,t,n);Ae=!!(e.flags&131072)}else Ae=!1,K&&t.flags&1048576&&yh(t,Ps,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;rs(e,t),e=t.pendingProps;var i=rr(t,Se.current);er(t,n),i=Gl(null,t,r,e,i,n);var s=Kl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Re(r)?(s=!0,ks(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Bl(t),i.updater=Js,t.stateNode=i,i._reactInternals=t,_a(t,r,e,n),t=Ia(null,t,r,!0,s,n)):(t.tag=0,K&&s&&Fl(t),Pe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(rs(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=jv(r),e=et(r,e),i){case 0:t=Oa(null,t,r,e,n);break e;case 1:t=Mc(null,t,r,e,n);break e;case 11:t=Lc(null,t,r,e,n);break e;case 14:t=Rc(null,t,r,et(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Oa(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Mc(e,t,r,i,n);case 3:e:{if(Xh(t),e===null)throw Error(N(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Ch(e,t),js(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=ar(Error(N(423)),t),t=Dc(e,t,r,n,i);break e}else if(r!==i){i=ar(Error(N(424)),t),t=Dc(e,t,r,n,i);break e}else for(_e=Xt(t.stateNode.containerInfo.firstChild),Ve=t,K=!0,rt=null,n=Sh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ir(),r===i){t=Rt(e,t,n);break e}Pe(e,t,r,n)}t=t.child}return t;case 5:return Ph(t),e===null&&Ma(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,ja(r,i)?o=null:s!==null&&ja(r,s)&&(t.flags|=32),Yh(e,t),Pe(e,t,o,n),t.child;case 6:return e===null&&Ma(t),null;case 13:return Zh(e,t,n);case 4:return $l(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=sr(t,null,r,n):Pe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Lc(e,t,r,i,n);case 7:return Pe(e,t,t.pendingProps,n),t.child;case 8:return Pe(e,t,t.pendingProps.children,n),t.child;case 12:return Pe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,$(Es,r._currentValue),r._currentValue=o,s!==null)if(at(s.value,o)){if(s.children===i.children&&!Le.current){t=Rt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Pt(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Da(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(N(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Da(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Pe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,er(t,n),i=Xe(i),r=r(i),t.flags|=1,Pe(e,t,r,n),t.child;case 14:return r=t.type,i=et(r,t.pendingProps),i=et(r.type,i),Rc(e,t,r,i,n);case 15:return Kh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),rs(e,t),t.tag=1,Re(r)?(e=!0,ks(t)):e=!1,er(t,n),Hh(t,r,i),_a(t,r,i,n),Ia(null,t,r,!0,e,n);case 19:return qh(e,t,n);case 22:return Qh(e,t,n)}throw Error(N(156,t.tag))};function pp(e,t){return Bd(e,t)}function Tv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qe(e,t,n,r){return new Tv(e,t,n,r)}function ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function jv(e){if(typeof e=="function")return ru(e)?1:0;if(e!=null){if(e=e.$$typeof,e===kl)return 11;if(e===Cl)return 14}return 2}function en(e,t){var n=e.alternate;return n===null?(n=Qe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function os(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")ru(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case _n:return Sn(n.children,i,s,t);case Sl:o=8,i|=8;break;case sa:return e=Qe(12,n,t,i|2),e.elementType=sa,e.lanes=s,e;case oa:return e=Qe(13,n,t,i),e.elementType=oa,e.lanes=s,e;case aa:return e=Qe(19,n,t,i),e.elementType=aa,e.lanes=s,e;case Pd:return no(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case kd:o=10;break e;case Cd:o=9;break e;case kl:o=11;break e;case Cl:o=14;break e;case bt:o=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=Qe(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Sn(e,t,n,r){return e=Qe(7,e,r,t),e.lanes=n,e}function no(e,t,n,r){return e=Qe(22,e,r,t),e.elementType=Pd,e.lanes=n,e.stateNode={isHidden:!1},e}function $o(e,t,n){return e=Qe(6,e,null,t),e.lanes=n,e}function Uo(e,t,n){return t=Qe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Nv(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Co(0),this.expirationTimes=Co(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Co(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function iu(e,t,n,r,i,s,o,a,l){return e=new Nv(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Qe(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Bl(s),e}function Av(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Fn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function mp(e){if(!e)return rn;e=e._reactInternals;e:{if(An(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Re(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(Re(n))return mh(e,n,t)}return t}function gp(e,t,n,r,i,s,o,a,l){return e=iu(n,r,!0,e,i,s,o,a,l),e.context=mp(null),n=e.current,r=Ee(),i=Jt(n),s=Pt(r,i),s.callback=t??null,Zt(n,s,i),e.current.lanes=i,gi(e,i,r),Me(e,r),e}function ro(e,t,n,r){var i=t.current,s=Ee(),o=Jt(i);return n=mp(n),t.context===null?t.context=n:t.pendingContext=n,t=Pt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Zt(i,t,o),e!==null&&(ot(e,i,o,s),es(e,i,o)),o}function _s(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Uc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function su(e,t){Uc(e,t),(e=e.alternate)&&Uc(e,t)}function Lv(){return null}var yp=typeof reportError=="function"?reportError:function(e){console.error(e)};function ou(e){this._internalRoot=e}io.prototype.render=ou.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));ro(e,t,null,null)};io.prototype.unmount=ou.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jn(function(){ro(null,e,null,null)}),t[At]=null}};function io(e){this._internalRoot=e}io.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ut.length&&t!==0&&t<Ut[n].priority;n++);Ut.splice(n,0,e),n===0&&Xd(e)}};function au(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function so(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Hc(){}function Rv(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=_s(o);s.call(u)}}var o=gp(t,r,e,0,null,!1,!1,"",Hc);return e._reactRootContainer=o,e[At]=o.current,ni(e.nodeType===8?e.parentNode:e),jn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=_s(l);a.call(u)}}var l=iu(e,0,!1,null,null,!1,!1,"",Hc);return e._reactRootContainer=l,e[At]=l.current,ni(e.nodeType===8?e.parentNode:e),jn(function(){ro(t,l,n,r)}),l}function oo(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=_s(o);a.call(l)}}ro(t,o,e,i)}else o=Rv(n,t,e,i,r);return _s(o)}Gd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rr(t.pendingLanes);n!==0&&(Tl(t,n|1),Me(t,ne()),!(z&6)&&(lr=ne()+500,un()))}break;case 13:jn(function(){var r=Lt(e,1);if(r!==null){var i=Ee();ot(r,e,1,i)}}),su(e,1)}};jl=function(e){if(e.tag===13){var t=Lt(e,134217728);if(t!==null){var n=Ee();ot(t,e,134217728,n)}su(e,134217728)}};Kd=function(e){if(e.tag===13){var t=Jt(e),n=Lt(e,t);if(n!==null){var r=Ee();ot(n,e,t,r)}su(e,t)}};Qd=function(){return B};Yd=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};ya=function(e,t,n){switch(t){case"input":if(ca(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Xs(r);if(!i)throw Error(N(90));Td(r),ca(r,i)}}}break;case"textarea":Nd(e,n);break;case"select":t=n.value,t!=null&&Xn(e,!!n.multiple,t,!1)}};_d=eu;Vd=jn;var Mv={usingClientEntryPoint:!1,Events:[vi,zn,Xs,Dd,Fd,eu]},Pr={findFiberByHostInstance:gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Dv={bundleType:Pr.bundleType,version:Pr.version,rendererPackageName:Pr.rendererPackageName,rendererConfig:Pr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ft.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zd(e),e===null?null:e.stateNode},findFiberByHostInstance:Pr.findFiberByHostInstance||Lv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ui=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ui.isDisabled&&Ui.supportsFiber)try{Gs=Ui.inject(Dv),ht=Ui}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mv;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!au(t))throw Error(N(200));return Av(e,t,null,n)};ze.createRoot=function(e,t){if(!au(e))throw Error(N(299));var n=!1,r="",i=yp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=iu(e,1,!1,null,null,n,!1,r,i),e[At]=t.current,ni(e.nodeType===8?e.parentNode:e),new ou(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=zd(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return jn(e)};ze.hydrate=function(e,t,n){if(!so(t))throw Error(N(200));return oo(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!au(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=yp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=gp(t,null,e,1,n??null,i,!1,s,o),e[At]=t.current,ni(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new io(t)};ze.render=function(e,t,n){if(!so(t))throw Error(N(200));return oo(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!so(e))throw Error(N(40));return e._reactRootContainer?(jn(function(){oo(null,null,e,!1,function(){e._reactRootContainer=null,e[At]=null})}),!0):!1};ze.unstable_batchedUpdates=eu;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!so(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return oo(e,t,n,!1,r)};ze.version="18.3.1-next-f1338f8080-20240426";function vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vp)}catch(e){console.error(e)}}vp(),vd.exports=ze;var Fv=vd.exports,Wc=Fv;ra.createRoot=Wc.createRoot,ra.hydrateRoot=Wc.hydrateRoot;var lu={};Object.defineProperty(lu,"__esModule",{value:!0});lu.parse=Bv;lu.serialize=$v;const _v=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Vv=/^[\u0021-\u003A\u003C-\u007E]*$/,Ov=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Iv=/^[\u0020-\u003A\u003D-\u007E]*$/,zv=Object.prototype.toString,bv=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Bv(e,t){const n=new bv,r=e.length;if(r<2)return n;const i=(t==null?void 0:t.decode)||Uv;let s=0;do{const o=e.indexOf("=",s);if(o===-1)break;const a=e.indexOf(";",s),l=a===-1?r:a;if(o>l){s=e.lastIndexOf(";",o-1)+1;continue}const u=Gc(e,s,o),c=Kc(e,o,u),h=e.slice(u,c);if(n[h]===void 0){let m=Gc(e,o+1,l),y=Kc(e,l,m);const x=i(e.slice(m,y));n[h]=x}s=l+1}while(s<r);return n}function Gc(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function Kc(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function $v(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!_v.test(e))throw new TypeError(`argument name is invalid: ${e}`);const i=r(t);if(!Vv.test(i))throw new TypeError(`argument val is invalid: ${t}`);let s=e+"="+i;if(!n)return s;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);s+="; Max-Age="+n.maxAge}if(n.domain){if(!Ov.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);s+="; Domain="+n.domain}if(n.path){if(!Iv.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);s+="; Path="+n.path}if(n.expires){if(!Hv(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.partitioned&&(s+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return s}function Uv(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Hv(e){return zv.call(e)==="[object Date]"}/**
 * react-router v7.4.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Qc="popstate";function Wv(e={}){function t(r,i){let{pathname:s,search:o,hash:a}=r.location;return Xa("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:fi(i)}return Kv(t,n,null,e)}function q(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function gt(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Gv(){return Math.random().toString(36).substring(2,10)}function Yc(e,t){return{usr:e.state,key:e.key,idx:t}}function Xa(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?pr(t):t,state:n,key:t&&t.key||r||Gv()}}function fi({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function pr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Kv(e,t,n,r={}){let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a="POP",l=null,u=c();u==null&&(u=0,o.replaceState({...o.state,idx:u},""));function c(){return(o.state||{idx:null}).idx}function h(){a="POP";let P=c(),g=P==null?null:P-u;u=P,l&&l({action:a,location:w.location,delta:g})}function m(P,g){a="PUSH";let d=Xa(w.location,P,g);u=c()+1;let f=Yc(d,u),v=w.createHref(d);try{o.pushState(f,"",v)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(v)}s&&l&&l({action:a,location:w.location,delta:1})}function y(P,g){a="REPLACE";let d=Xa(w.location,P,g);u=c();let f=Yc(d,u),v=w.createHref(d);o.replaceState(f,"",v),s&&l&&l({action:a,location:w.location,delta:0})}function x(P){let g=i.location.origin!=="null"?i.location.origin:i.location.href,d=typeof P=="string"?P:fi(P);return d=d.replace(/ $/,"%20"),q(g,`No window.location.(origin|href) available to create URL for href: ${d}`),new URL(d,g)}let w={get action(){return a},get location(){return e(i,o)},listen(P){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Qc,h),l=P,()=>{i.removeEventListener(Qc,h),l=null}},createHref(P){return t(i,P)},createURL:x,encodeLocation(P){let g=x(P);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:m,replace:y,go(P){return o.go(P)}};return w}function xp(e,t,n="/"){return Qv(e,t,n,!1)}function Qv(e,t,n,r){let i=typeof t=="string"?pr(t):t,s=Mt(i.pathname||"/",n);if(s==null)return null;let o=wp(e);Yv(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=o0(s);a=i0(o[l],u,r)}return a}function wp(e,t=[],n=[],r=""){let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(q(l.relativePath.startsWith(r),`Absolute route path "${l.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(r.length));let u=Et([r,l.relativePath]),c=n.concat(l);s.children&&s.children.length>0&&(q(s.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),wp(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:n0(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of Sp(s.path))i(s,o,l)}),t}function Sp(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=Sp(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function Yv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:r0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var Xv=/^:[\w-]+$/,Zv=3,qv=2,Jv=1,e0=10,t0=-2,Xc=e=>e==="*";function n0(e,t){let n=e.split("/"),r=n.length;return n.some(Xc)&&(r+=t0),t&&(r+=qv),n.filter(i=>!Xc(i)).reduce((i,s)=>i+(Xv.test(s)?Zv:s===""?Jv:e0),r)}function r0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function i0(e,t,n=!1){let{routesMeta:r}=e,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",h=Vs({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),m=l.route;if(!h&&u&&n&&!r[r.length-1].route.index&&(h=Vs({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:Et([s,h.pathname]),pathnameBase:c0(Et([s,h.pathnameBase])),route:m}),h.pathnameBase!=="/"&&(s=Et([s,h.pathnameBase]))}return o}function Vs(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=s0(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,{paramName:c,isOptional:h},m)=>{if(c==="*"){let x=a[m]||"";o=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const y=a[m];return h&&!y?u[c]=void 0:u[c]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function s0(e,t=!1,n=!0){gt(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function o0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return gt(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Mt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function a0(e,t="/"){let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?pr(e):e;return{pathname:n?n.startsWith("/")?n:l0(n,t):t,search:f0(r),hash:d0(i)}}function l0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ho(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function u0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function kp(e){let t=u0(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function Cp(e,t,n,r=!1){let i;typeof e=="string"?i=pr(e):(i={...e},q(!i.pathname||!i.pathname.includes("?"),Ho("?","pathname","search",i)),q(!i.pathname||!i.pathname.includes("#"),Ho("#","pathname","hash",i)),q(!i.search||!i.search.includes("#"),Ho("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let h=t.length-1;if(!r&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),h-=1;i.pathname=m.join("/")}a=h>=0?t[h]:"/"}let l=a0(i,a),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}var Et=e=>e.join("/").replace(/\/\/+/g,"/"),c0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),f0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,d0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function h0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Pp=["POST","PUT","PATCH","DELETE"];new Set(Pp);var p0=["GET",...Pp];new Set(p0);var mr=C.createContext(null);mr.displayName="DataRouter";var ao=C.createContext(null);ao.displayName="DataRouterState";var Ep=C.createContext({isTransitioning:!1});Ep.displayName="ViewTransition";var m0=C.createContext(new Map);m0.displayName="Fetchers";var g0=C.createContext(null);g0.displayName="Await";var yt=C.createContext(null);yt.displayName="Navigation";var wi=C.createContext(null);wi.displayName="Location";var _t=C.createContext({outlet:null,matches:[],isDataRoute:!1});_t.displayName="Route";var uu=C.createContext(null);uu.displayName="RouteError";function y0(e,{relative:t}={}){q(Si(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=C.useContext(yt),{hash:i,pathname:s,search:o}=ki(e,{relative:t}),a=s;return n!=="/"&&(a=s==="/"?n:Et([n,s])),r.createHref({pathname:a,search:o,hash:i})}function Si(){return C.useContext(wi)!=null}function Vt(){return q(Si(),"useLocation() may be used only in the context of a <Router> component."),C.useContext(wi).location}var Tp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function jp(e){C.useContext(yt).static||C.useLayoutEffect(e)}function Np(){let{isDataRoute:e}=C.useContext(_t);return e?L0():v0()}function v0(){q(Si(),"useNavigate() may be used only in the context of a <Router> component.");let e=C.useContext(mr),{basename:t,navigator:n}=C.useContext(yt),{matches:r}=C.useContext(_t),{pathname:i}=Vt(),s=JSON.stringify(kp(r)),o=C.useRef(!1);return jp(()=>{o.current=!0}),C.useCallback((l,u={})=>{if(gt(o.current,Tp),!o.current)return;if(typeof l=="number"){n.go(l);return}let c=Cp(l,JSON.parse(s),i,u.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:Et([t,c.pathname])),(u.replace?n.replace:n.push)(c,u.state,u)},[t,n,s,i,e])}C.createContext(null);function ki(e,{relative:t}={}){let{matches:n}=C.useContext(_t),{pathname:r}=Vt(),i=JSON.stringify(kp(n));return C.useMemo(()=>Cp(e,JSON.parse(i),r,t==="path"),[e,i,r,t])}function x0(e,t){return Ap(e,t)}function Ap(e,t,n,r){var d;q(Si(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i,static:s}=C.useContext(yt),{matches:o}=C.useContext(_t),a=o[o.length-1],l=a?a.params:{},u=a?a.pathname:"/",c=a?a.pathnameBase:"/",h=a&&a.route;{let f=h&&h.path||"";Lp(u,!h||f.endsWith("*")||f.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${f}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${f}"> to <Route path="${f==="/"?"*":`${f}/*`}">.`)}let m=Vt(),y;if(t){let f=typeof t=="string"?pr(t):t;q(c==="/"||((d=f.pathname)==null?void 0:d.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${f.pathname}" was given in the \`location\` prop.`),y=f}else y=m;let x=y.pathname||"/",w=x;if(c!=="/"){let f=c.replace(/^\//,"").split("/");w="/"+x.replace(/^\//,"").split("/").slice(f.length).join("/")}let P=!s&&n&&n.matches&&n.matches.length>0?n.matches:xp(e,{pathname:w});gt(h||P!=null,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),gt(P==null||P[P.length-1].route.element!==void 0||P[P.length-1].route.Component!==void 0||P[P.length-1].route.lazy!==void 0,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let g=P0(P&&P.map(f=>Object.assign({},f,{params:Object.assign({},l,f.params),pathname:Et([c,i.encodeLocation?i.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?c:Et([c,i.encodeLocation?i.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),o,n,r);return t&&g?C.createElement(wi.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...y},navigationType:"POP"}},g):g}function w0(){let e=A0(),t=h0(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:r},s={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=C.createElement(C.Fragment,null,C.createElement("p",null,"💿 Hey developer 👋"),C.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",C.createElement("code",{style:s},"ErrorBoundary")," or"," ",C.createElement("code",{style:s},"errorElement")," prop on your route.")),C.createElement(C.Fragment,null,C.createElement("h2",null,"Unexpected Application Error!"),C.createElement("h3",{style:{fontStyle:"italic"}},t),n?C.createElement("pre",{style:i},n):null,o)}var S0=C.createElement(w0,null),k0=class extends C.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?C.createElement(_t.Provider,{value:this.props.routeContext},C.createElement(uu.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function C0({routeContext:e,match:t,children:n}){let r=C.useContext(mr);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),C.createElement(_t.Provider,{value:e},n)}function P0(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,s=n==null?void 0:n.errors;if(s!=null){let l=i.findIndex(u=>u.route.id&&(s==null?void 0:s[u.route.id])!==void 0);q(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(s).join(",")}`),i=i.slice(0,Math.min(i.length,l+1))}let o=!1,a=-1;if(n)for(let l=0;l<i.length;l++){let u=i[l];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(a=l),u.route.id){let{loaderData:c,errors:h}=n,m=u.route.loader&&!c.hasOwnProperty(u.route.id)&&(!h||h[u.route.id]===void 0);if(u.route.lazy||m){o=!0,a>=0?i=i.slice(0,a+1):i=[i[0]];break}}}return i.reduceRight((l,u,c)=>{let h,m=!1,y=null,x=null;n&&(h=s&&u.route.id?s[u.route.id]:void 0,y=u.route.errorElement||S0,o&&(a<0&&c===0?(Lp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),m=!0,x=null):a===c&&(m=!0,x=u.route.hydrateFallbackElement||null)));let w=t.concat(i.slice(0,c+1)),P=()=>{let g;return h?g=y:m?g=x:u.route.Component?g=C.createElement(u.route.Component,null):u.route.element?g=u.route.element:g=l,C.createElement(C0,{match:u,routeContext:{outlet:l,matches:w,isDataRoute:n!=null},children:g})};return n&&(u.route.ErrorBoundary||u.route.errorElement||c===0)?C.createElement(k0,{location:n.location,revalidation:n.revalidation,component:y,error:h,children:P(),routeContext:{outlet:null,matches:w,isDataRoute:!0}}):P()},null)}function cu(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function E0(e){let t=C.useContext(mr);return q(t,cu(e)),t}function T0(e){let t=C.useContext(ao);return q(t,cu(e)),t}function j0(e){let t=C.useContext(_t);return q(t,cu(e)),t}function fu(e){let t=j0(e),n=t.matches[t.matches.length-1];return q(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function N0(){return fu("useRouteId")}function A0(){var r;let e=C.useContext(uu),t=T0("useRouteError"),n=fu("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function L0(){let{router:e}=E0("useNavigate"),t=fu("useNavigate"),n=C.useRef(!1);return jp(()=>{n.current=!0}),C.useCallback(async(i,s={})=>{gt(n.current,Tp),n.current&&(typeof i=="number"?e.navigate(i):await e.navigate(i,{fromRouteId:t,...s}))},[e,t])}var Zc={};function Lp(e,t,n){!t&&!Zc[e]&&(Zc[e]=!0,gt(!1,n))}C.memo(R0);function R0({routes:e,future:t,state:n}){return Ap(e,void 0,n,t)}function Za(e){q(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function M0({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:i,static:s=!1}){q(!Si(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),a=C.useMemo(()=>({basename:o,navigator:i,static:s,future:{}}),[o,i,s]);typeof n=="string"&&(n=pr(n));let{pathname:l="/",search:u="",hash:c="",state:h=null,key:m="default"}=n,y=C.useMemo(()=>{let x=Mt(l,o);return x==null?null:{location:{pathname:x,search:u,hash:c,state:h,key:m},navigationType:r}},[o,l,u,c,h,m,r]);return gt(y!=null,`<Router basename="${o}"> is not able to match the URL "${l}${u}${c}" because it does not start with the basename, so the <Router> won't render anything.`),y==null?null:C.createElement(yt.Provider,{value:a},C.createElement(wi.Provider,{children:t,value:y}))}function D0({children:e,location:t}){return x0(qa(e),t)}function qa(e,t=[]){let n=[];return C.Children.forEach(e,(r,i)=>{if(!C.isValidElement(r))return;let s=[...t,i];if(r.type===C.Fragment){n.push.apply(n,qa(r.props.children,s));return}q(r.type===Za,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),q(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=qa(r.props.children,s)),n.push(o)}),n}var as="get",ls="application/x-www-form-urlencoded";function lo(e){return e!=null&&typeof e.tagName=="string"}function F0(e){return lo(e)&&e.tagName.toLowerCase()==="button"}function _0(e){return lo(e)&&e.tagName.toLowerCase()==="form"}function V0(e){return lo(e)&&e.tagName.toLowerCase()==="input"}function O0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function I0(e,t){return e.button===0&&(!t||t==="_self")&&!O0(e)}var Hi=null;function z0(){if(Hi===null)try{new FormData(document.createElement("form"),0),Hi=!1}catch{Hi=!0}return Hi}var b0=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Wo(e){return e!=null&&!b0.has(e)?(gt(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ls}"`),null):e}function B0(e,t){let n,r,i,s,o;if(_0(e)){let a=e.getAttribute("action");r=a?Mt(a,t):null,n=e.getAttribute("method")||as,i=Wo(e.getAttribute("enctype"))||ls,s=new FormData(e)}else if(F0(e)||V0(e)&&(e.type==="submit"||e.type==="image")){let a=e.form;if(a==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||a.getAttribute("action");if(r=l?Mt(l,t):null,n=e.getAttribute("formmethod")||a.getAttribute("method")||as,i=Wo(e.getAttribute("formenctype"))||Wo(a.getAttribute("enctype"))||ls,s=new FormData(a,e),!z0()){let{name:u,type:c,value:h}=e;if(c==="image"){let m=u?`${u}.`:"";s.append(`${m}x`,"0"),s.append(`${m}y`,"0")}else u&&s.append(u,h)}}else{if(lo(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=as,r=null,i=ls,o=e}return s&&i==="text/plain"&&(o=s,s=void 0),{action:r,method:n.toLowerCase(),encType:i,formData:s,body:o}}function du(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function $0(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function U0(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function H0(e,t,n){let r=await Promise.all(e.map(async i=>{let s=t.routes[i.route.id];if(s){let o=await $0(s,n);return o.links?o.links():[]}return[]}));return Q0(r.flat(1).filter(U0).filter(i=>i.rel==="stylesheet"||i.rel==="preload").map(i=>i.rel==="stylesheet"?{...i,rel:"prefetch",as:"style"}:{...i,rel:"prefetch"}))}function qc(e,t,n,r,i,s){let o=(l,u)=>n[u]?l.route.id!==n[u].route.id:!0,a=(l,u)=>{var c;return n[u].pathname!==l.pathname||((c=n[u].route.path)==null?void 0:c.endsWith("*"))&&n[u].params["*"]!==l.params["*"]};return s==="assets"?t.filter((l,u)=>o(l,u)||a(l,u)):s==="data"?t.filter((l,u)=>{var h;let c=r.routes[l.route.id];if(!c||!c.hasLoader)return!1;if(o(l,u)||a(l,u))return!0;if(l.route.shouldRevalidate){let m=l.route.shouldRevalidate({currentUrl:new URL(i.pathname+i.search+i.hash,window.origin),currentParams:((h=n[0])==null?void 0:h.params)||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof m=="boolean")return m}return!0}):[]}function W0(e,t,{includeHydrateFallback:n}={}){return G0(e.map(r=>{let i=t.routes[r.route.id];if(!i)return[];let s=[i.module];return i.clientActionModule&&(s=s.concat(i.clientActionModule)),i.clientLoaderModule&&(s=s.concat(i.clientLoaderModule)),n&&i.hydrateFallbackModule&&(s=s.concat(i.hydrateFallbackModule)),i.imports&&(s=s.concat(i.imports)),s}).flat(1))}function G0(e){return[...new Set(e)]}function K0(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function Q0(e,t){let n=new Set;return new Set(t),e.reduce((r,i)=>{let s=JSON.stringify(K0(i));return n.has(s)||(n.add(s),r.push({key:s,link:i})),r},[])}function Y0(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&Mt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function Rp(){let e=C.useContext(mr);return du(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function X0(){let e=C.useContext(ao);return du(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var hu=C.createContext(void 0);hu.displayName="FrameworkContext";function Mp(){let e=C.useContext(hu);return du(e,"You must render this element inside a <HydratedRouter> element"),e}function Z0(e,t){let n=C.useContext(hu),[r,i]=C.useState(!1),[s,o]=C.useState(!1),{onFocus:a,onBlur:l,onMouseEnter:u,onMouseLeave:c,onTouchStart:h}=t,m=C.useRef(null);C.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let w=g=>{g.forEach(d=>{o(d.isIntersecting)})},P=new IntersectionObserver(w,{threshold:.5});return m.current&&P.observe(m.current),()=>{P.disconnect()}}},[e]),C.useEffect(()=>{if(r){let w=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(w)}}},[r]);let y=()=>{i(!0)},x=()=>{i(!1),o(!1)};return n?e!=="intent"?[s,m,{}]:[s,m,{onFocus:Er(a,y),onBlur:Er(l,x),onMouseEnter:Er(u,y),onMouseLeave:Er(c,x),onTouchStart:Er(h,y)}]:[!1,m,{}]}function Er(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function q0({page:e,...t}){let{router:n}=Rp(),r=C.useMemo(()=>xp(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?C.createElement(ex,{page:e,matches:r,...t}):null}function J0(e){let{manifest:t,routeModules:n}=Mp(),[r,i]=C.useState([]);return C.useEffect(()=>{let s=!1;return H0(e,t,n).then(o=>{s||i(o)}),()=>{s=!0}},[e,t,n]),r}function ex({page:e,matches:t,...n}){let r=Vt(),{manifest:i,routeModules:s}=Mp(),{basename:o}=Rp(),{loaderData:a,matches:l}=X0(),u=C.useMemo(()=>qc(e,t,l,i,r,"data"),[e,t,l,i,r]),c=C.useMemo(()=>qc(e,t,l,i,r,"assets"),[e,t,l,i,r]),h=C.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let x=new Set,w=!1;if(t.forEach(g=>{var f;let d=i.routes[g.route.id];!d||!d.hasLoader||(!u.some(v=>v.route.id===g.route.id)&&g.route.id in a&&((f=s[g.route.id])!=null&&f.shouldRevalidate)||d.hasClientLoader?w=!0:x.add(g.route.id))}),x.size===0)return[];let P=Y0(e,o);return w&&x.size>0&&P.searchParams.set("_routes",t.filter(g=>x.has(g.route.id)).map(g=>g.route.id).join(",")),[P.pathname+P.search]},[o,a,r,i,u,t,e,s]),m=C.useMemo(()=>W0(c,i),[c,i]),y=J0(c);return C.createElement(C.Fragment,null,h.map(x=>C.createElement("link",{key:x,rel:"prefetch",as:"fetch",href:x,...n})),m.map(x=>C.createElement("link",{key:x,rel:"modulepreload",href:x,...n})),y.map(({key:x,link:w})=>C.createElement("link",{key:x,...w})))}function tx(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var Dp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Dp&&(window.__reactRouterVersion="7.4.0")}catch{}function nx({basename:e,children:t,window:n}){let r=C.useRef();r.current==null&&(r.current=Wv({window:n,v5Compat:!0}));let i=r.current,[s,o]=C.useState({action:i.action,location:i.location}),a=C.useCallback(l=>{C.startTransition(()=>o(l))},[o]);return C.useLayoutEffect(()=>i.listen(a),[i,a]),C.createElement(M0,{basename:e,children:t,location:s.location,navigationType:s.action,navigator:i})}var Fp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,it=C.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:c,viewTransition:h,...m},y){let{basename:x}=C.useContext(yt),w=typeof u=="string"&&Fp.test(u),P,g=!1;if(typeof u=="string"&&w&&(P=u,Dp))try{let j=new URL(window.location.href),L=u.startsWith("//")?new URL(j.protocol+u):new URL(u),V=Mt(L.pathname,x);L.origin===j.origin&&V!=null?u=V+L.search+L.hash:g=!0}catch{gt(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let d=y0(u,{relative:i}),[f,v,S]=Z0(r,m),k=ox(u,{replace:o,state:a,target:l,preventScrollReset:c,relative:i,viewTransition:h});function E(j){t&&t(j),j.defaultPrevented||k(j)}let T=C.createElement("a",{...m,...S,href:P||d,onClick:g||s?t:E,ref:tx(y,v),target:l,"data-discover":!w&&n==="render"?"true":void 0});return f&&!w?C.createElement(C.Fragment,null,T,C.createElement(q0,{page:d})):T});it.displayName="Link";var rx=C.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:i=!1,style:s,to:o,viewTransition:a,children:l,...u},c){let h=ki(o,{relative:u.relative}),m=Vt(),y=C.useContext(ao),{navigator:x,basename:w}=C.useContext(yt),P=y!=null&&fx(h)&&a===!0,g=x.encodeLocation?x.encodeLocation(h).pathname:h.pathname,d=m.pathname,f=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;n||(d=d.toLowerCase(),f=f?f.toLowerCase():null,g=g.toLowerCase()),f&&w&&(f=Mt(f,w)||f);const v=g!=="/"&&g.endsWith("/")?g.length-1:g.length;let S=d===g||!i&&d.startsWith(g)&&d.charAt(v)==="/",k=f!=null&&(f===g||!i&&f.startsWith(g)&&f.charAt(g.length)==="/"),E={isActive:S,isPending:k,isTransitioning:P},T=S?t:void 0,j;typeof r=="function"?j=r(E):j=[r,S?"active":null,k?"pending":null,P?"transitioning":null].filter(Boolean).join(" ");let L=typeof s=="function"?s(E):s;return C.createElement(it,{...u,"aria-current":T,className:j,ref:c,style:L,to:o,viewTransition:a},typeof l=="function"?l(E):l)});rx.displayName="NavLink";var ix=C.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:i,state:s,method:o=as,action:a,onSubmit:l,relative:u,preventScrollReset:c,viewTransition:h,...m},y)=>{let x=ux(),w=cx(a,{relative:u}),P=o.toLowerCase()==="get"?"get":"post",g=typeof a=="string"&&Fp.test(a),d=f=>{if(l&&l(f),f.defaultPrevented)return;f.preventDefault();let v=f.nativeEvent.submitter,S=(v==null?void 0:v.getAttribute("formmethod"))||o;x(v||f.currentTarget,{fetcherKey:t,method:S,navigate:n,replace:i,state:s,relative:u,preventScrollReset:c,viewTransition:h})};return C.createElement("form",{ref:y,method:P,action:w,onSubmit:r?l:d,...m,"data-discover":!g&&e==="render"?"true":void 0})});ix.displayName="Form";function sx(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function _p(e){let t=C.useContext(mr);return q(t,sx(e)),t}function ox(e,{target:t,replace:n,state:r,preventScrollReset:i,relative:s,viewTransition:o}={}){let a=Np(),l=Vt(),u=ki(e,{relative:s});return C.useCallback(c=>{if(I0(c,t)){c.preventDefault();let h=n!==void 0?n:fi(l)===fi(u);a(e,{replace:h,state:r,preventScrollReset:i,relative:s,viewTransition:o})}},[l,a,u,n,r,t,e,i,s,o])}var ax=0,lx=()=>`__${String(++ax)}__`;function ux(){let{router:e}=_p("useSubmit"),{basename:t}=C.useContext(yt),n=N0();return C.useCallback(async(r,i={})=>{let{action:s,method:o,encType:a,formData:l,body:u}=B0(r,t);if(i.navigate===!1){let c=i.fetcherKey||lx();await e.fetch(c,n,i.action||s,{preventScrollReset:i.preventScrollReset,formData:l,body:u,formMethod:i.method||o,formEncType:i.encType||a,flushSync:i.flushSync})}else await e.navigate(i.action||s,{preventScrollReset:i.preventScrollReset,formData:l,body:u,formMethod:i.method||o,formEncType:i.encType||a,replace:i.replace,state:i.state,fromRouteId:n,flushSync:i.flushSync,viewTransition:i.viewTransition})},[e,t,n])}function cx(e,{relative:t}={}){let{basename:n}=C.useContext(yt),r=C.useContext(_t);q(r,"useFormAction must be used inside a RouteContext");let[i]=r.matches.slice(-1),s={...ki(e||".",{relative:t})},o=Vt();if(e==null){s.search=o.search;let a=new URLSearchParams(s.search),l=a.getAll("index");if(l.some(c=>c==="")){a.delete("index"),l.filter(h=>h).forEach(h=>a.append("index",h));let c=a.toString();s.search=c?`?${c}`:""}}return(!e||e===".")&&i.route.index&&(s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(s.pathname=s.pathname==="/"?n:Et([n,s.pathname])),fi(s)}function fx(e,t={}){let n=C.useContext(Ep);q(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=_p("useViewTransitionState"),i=ki(e,{relative:t.relative});if(!n.isTransitioning)return!1;let s=Mt(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=Mt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Vs(i.pathname,o)!=null||Vs(i.pathname,s)!=null}new TextEncoder;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var dx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),G=(e,t)=>{const n=C.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:a="",children:l,...u},c)=>C.createElement("svg",{ref:c,...dx,width:i,height:i,stroke:r,strokeWidth:o?Number(s)*24/Number(i):s,className:["lucide",`lucide-${hx(e)}`,a].join(" "),...u},[...t.map(([h,m])=>C.createElement(h,m)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const px=G("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=G("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=G("BookCheck",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=G("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=G("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xx=G("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const he=G("CheckCircle2",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=G("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vp=G("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=G("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=G("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=G("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=G("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ja=G("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=G("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=G("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Px=G("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=G("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=G("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=G("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=G("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=G("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zp=G("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lx=G("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=G("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Mx="/assets/qochi_logo-BtxJES_w.png",Dx=()=>{const[e,t]=C.useState(!1),[n,r]=C.useState(!1),i=Vt();return C.useEffect(()=>{const s=()=>{r(window.scrollY>20)};return window.addEventListener("scroll",s),()=>window.removeEventListener("scroll",s)},[]),p.jsx("nav",{className:`fixed w-full top-0 z-50 transition-all duration-300 border-b border-blue-500/20 ${n?"bg-background/95 backdrop-blur-sm shadow-lg":"bg-transparent"}`,children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsxs("div",{className:"flex justify-between items-center py-4",children:[p.jsxs(it,{to:"/",className:"flex items-center space-x-2 group",children:[p.jsx("img",{src:Mx,alt:"Qochi Logo",className:"h-12 w-12 transition-transform duration-300 group-hover:rotate-12"}),p.jsx("span",{className:"text-xl font-bold text-white hover:text-blue-400 transition-colors duration-300",children:"Qochi Services"})]}),p.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[p.jsx(it,{to:"/",className:`relative text-white hover:text-blue-400 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-400 after:transition-all after:duration-300 hover:after:w-full ${i.pathname==="/"?"text-blue-400 after:w-full":""}`,children:"Home"}),p.jsx(it,{to:"/contact",className:`relative text-white hover:text-blue-400 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-400 after:transition-all after:duration-300 hover:after:w-full ${i.pathname==="/contact"?"text-blue-400 after:w-full":""}`,children:"Contact"}),p.jsx("a",{href:"#pricing",className:"bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105",children:"Book a Session"})]}),p.jsx("div",{className:"md:hidden",children:p.jsx("button",{onClick:()=>t(!e),className:"text-white hover:text-blue-400 focus:outline-none transition-colors duration-300",children:e?p.jsx(Lx,{className:"h-6 w-6"}):p.jsx(kx,{className:"h-6 w-6"})})})]}),p.jsx("div",{className:`md:hidden transition-all duration-300 ease-in-out ${e?"max-h-64 opacity-100 visible":"max-h-0 opacity-0 invisible"}`,children:p.jsxs("div",{className:"py-4 border-t border-blue-500/20 space-y-4",children:[p.jsx(it,{to:"/",className:`block text-white hover:text-blue-400 transition-colors duration-300 ${i.pathname==="/"?"text-blue-400":""}`,onClick:()=>t(!1),children:"Home"}),p.jsx(it,{to:"/contact",className:`block text-white hover:text-blue-400 transition-colors duration-300 ${i.pathname==="/contact"?"text-blue-400":""}`,onClick:()=>t(!1),children:"Contact"}),p.jsx("a",{href:"#pricing",className:"block bg-blue-500 hover:bg-blue-600 text-white text-center font-medium py-2 px-6 rounded-lg transition-all duration-300",onClick:()=>t(!1),children:"Book a Session"})]})})]})})},Fx=()=>{const e=()=>{window.scrollTo({top:0,behavior:"smooth"})};return p.jsx("footer",{className:"bg-secondary pt-12 pb-8",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8",children:[p.jsxs("div",{children:[p.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[p.jsx(Op,{className:"h-6 w-6 text-accent"}),p.jsx("h3",{className:"text-xl font-bold",children:"Qochi services"})]}),p.jsx("p",{className:"text-gray-300 mb-4",children:"Professional C programming tutoring services to help you master the fundamentals and advanced concepts of C programming."}),p.jsxs("div",{className:"flex space-x-4",children:[p.jsx("a",{href:"https://github.com/AppleItsme/",className:"text-gray-400 hover:text-accent transition-colors",children:p.jsx(Ip,{className:"h-5 w-5"})}),p.jsx("a",{href:"#",className:"text-gray-400 hover:text-accent transition-colors",children:p.jsx(Sx,{className:"h-5 w-5"})})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),p.jsxs("ul",{className:"space-y-2",children:[p.jsx("li",{children:p.jsx(it,{to:"/",className:"text-gray-300 hover:text-accent transition-colors",children:"Home"})}),p.jsx("li",{children:p.jsx(it,{to:"/contact",className:"text-gray-300 hover:text-accent transition-colors",onClick:e,children:"Contact"})}),p.jsx("li",{children:p.jsx("a",{href:"#",className:"text-gray-300 hover:text-accent transition-colors",children:"Resources"})}),p.jsx("li",{children:p.jsx("a",{href:"#",className:"text-gray-300 hover:text-accent transition-colors",children:"Testimonials"})}),p.jsx("li",{children:p.jsx("a",{href:"#",className:"text-gray-300 hover:text-accent transition-colors",children:"Terms & Conditions"})}),p.jsx("li",{children:p.jsx("a",{href:"#",className:"text-gray-300 hover:text-accent transition-colors",children:"FAQ"})})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Contact Information"}),p.jsxs("ul",{className:"space-y-3",children:[p.jsxs("li",{className:"flex items-start space-x-3",children:[p.jsx(Ja,{className:"h-5 w-5 text-accent mt-0.5"}),p.jsx("span",{className:"text-gray-300",children:"<EMAIL>"})]}),p.jsxs("li",{className:"flex items-start space-x-3",children:[p.jsx(Px,{className:"h-5 w-5 text-accent mt-0.5"}),p.jsx("span",{className:"text-gray-300",children:"+1 (555) 123-4567"})]})]})]})]}),p.jsx("div",{className:"pt-8 border-t border-gray-700 text-center text-gray-400",children:p.jsxs("p",{children:["© ",new Date().getFullYear()," Qochi Services. All rights reserved."]})})]})})},bp=C.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),uo=C.createContext({}),pu=C.createContext(null),co=typeof document<"u",_x=co?C.useLayoutEffect:C.useEffect,Bp=C.createContext({strict:!1}),mu=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Vx="framerAppearId",$p="data-"+mu(Vx);function Ox(e,t,n,r){const{visualElement:i}=C.useContext(uo),s=C.useContext(Bp),o=C.useContext(pu),a=C.useContext(bp).reducedMotion,l=C.useRef();r=r||s.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;C.useInsertionEffect(()=>{u&&u.update(n,o)});const c=C.useRef(!!(n[$p]&&!window.HandoffComplete));return _x(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),C.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function Gn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Ix(e,t,n){return C.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Gn(n)&&(n.current=r))},[t])}function di(e){return typeof e=="string"||Array.isArray(e)}function fo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const gu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],yu=["initial",...gu];function ho(e){return fo(e.animate)||yu.some(t=>di(e[t]))}function Up(e){return!!(ho(e)||e.variants)}function zx(e,t){if(ho(e)){const{initial:n,animate:r}=e;return{initial:n===!1||di(n)?n:void 0,animate:di(r)?r:void 0}}return e.inherit!==!1?t:{}}function bx(e){const{initial:t,animate:n}=zx(e,C.useContext(uo));return C.useMemo(()=>({initial:t,animate:n}),[Jc(t),Jc(n)])}function Jc(e){return Array.isArray(e)?e.join(" "):e}const ef={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},hi={};for(const e in ef)hi[e]={isEnabled:t=>ef[e].some(n=>!!t[n])};function Bx(e){for(const t in e)hi[t]={...hi[t],...e[t]}}const Hp=C.createContext({}),Wp=C.createContext({}),$x=Symbol.for("motionComponentSymbol");function Ux({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Bx(e);function s(a,l){let u;const c={...C.useContext(bp),...a,layoutId:Hx(a)},{isStatic:h}=c,m=bx(a),y=r(a,h);if(!h&&co){m.visualElement=Ox(i,y,c,t);const x=C.useContext(Wp),w=C.useContext(Bp).strict;m.visualElement&&(u=m.visualElement.loadFeatures(c,w,e,x))}return C.createElement(uo.Provider,{value:m},u&&m.visualElement?C.createElement(u,{visualElement:m.visualElement,...c}):null,n(i,a,Ix(y,m.visualElement,l),y,h,m.visualElement))}const o=C.forwardRef(s);return o[$x]=i,o}function Hx({layoutId:e}){const t=C.useContext(Hp).id;return t&&e!==void 0?t+"-"+e:e}function Wx(e){function t(r,i={}){return Ux(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Gx=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function vu(e){return typeof e!="string"||e.includes("-")?!1:!!(Gx.indexOf(e)>-1||/[A-Z]/.test(e))}const Os={};function Kx(e){Object.assign(Os,e)}const Ci=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ln=new Set(Ci);function Gp(e,{layout:t,layoutId:n}){return Ln.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Os[e]||e==="opacity")}const De=e=>!!(e&&e.getVelocity),Qx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Yx=Ci.length;function Xx(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let s="";for(let o=0;o<Yx;o++){const a=Ci[o];if(e[a]!==void 0){const l=Qx[a]||a;s+=`${l}(${e[a]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,r?"":s):n&&r&&(s="none"),s}const Kp=e=>t=>typeof t=="string"&&t.startsWith(e),Qp=Kp("--"),el=Kp("var(--"),Zx=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,qx=(e,t)=>t&&typeof e=="number"?t.transform(e):e,sn=(e,t,n)=>Math.min(Math.max(n,e),t),Rn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Hr={...Rn,transform:e=>sn(0,1,e)},Wi={...Rn,default:1},Wr=e=>Math.round(e*1e5)/1e5,po=/(-)?([\d]*\.?[\d])+/g,Yp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Jx=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Pi(e){return typeof e=="string"}const Ei=e=>({test:t=>Pi(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),zt=Ei("deg"),mt=Ei("%"),F=Ei("px"),e1=Ei("vh"),t1=Ei("vw"),tf={...mt,parse:e=>mt.parse(e)/100,transform:e=>mt.transform(e*100)},nf={...Rn,transform:Math.round},Xp={borderWidth:F,borderTopWidth:F,borderRightWidth:F,borderBottomWidth:F,borderLeftWidth:F,borderRadius:F,radius:F,borderTopLeftRadius:F,borderTopRightRadius:F,borderBottomRightRadius:F,borderBottomLeftRadius:F,width:F,maxWidth:F,height:F,maxHeight:F,size:F,top:F,right:F,bottom:F,left:F,padding:F,paddingTop:F,paddingRight:F,paddingBottom:F,paddingLeft:F,margin:F,marginTop:F,marginRight:F,marginBottom:F,marginLeft:F,rotate:zt,rotateX:zt,rotateY:zt,rotateZ:zt,scale:Wi,scaleX:Wi,scaleY:Wi,scaleZ:Wi,skew:zt,skewX:zt,skewY:zt,distance:F,translateX:F,translateY:F,translateZ:F,x:F,y:F,z:F,perspective:F,transformPerspective:F,opacity:Hr,originX:tf,originY:tf,originZ:F,zIndex:nf,fillOpacity:Hr,strokeOpacity:Hr,numOctaves:nf};function xu(e,t,n,r){const{style:i,vars:s,transform:o,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const h in t){const m=t[h];if(Qp(h)){s[h]=m;continue}const y=Xp[h],x=qx(m,y);if(Ln.has(h)){if(l=!0,o[h]=x,!c)continue;m!==(y.default||0)&&(c=!1)}else h.startsWith("origin")?(u=!0,a[h]=x):i[h]=x}if(t.transform||(l||r?i.transform=Xx(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:h="50%",originY:m="50%",originZ:y=0}=a;i.transformOrigin=`${h} ${m} ${y}`}}const wu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Zp(e,t,n){for(const r in t)!De(t[r])&&!Gp(r,n)&&(e[r]=t[r])}function n1({transformTemplate:e},t,n){return C.useMemo(()=>{const r=wu();return xu(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function r1(e,t,n){const r=e.style||{},i={};return Zp(i,r,e),Object.assign(i,n1(e,t,n)),e.transformValues?e.transformValues(i):i}function i1(e,t,n){const r={},i=r1(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const s1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Is(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||s1.has(e)}let qp=e=>!Is(e);function o1(e){e&&(qp=t=>t.startsWith("on")?!Is(t):e(t))}try{o1(require("@emotion/is-prop-valid").default)}catch{}function a1(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(qp(i)||n===!0&&Is(i)||!t&&!Is(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function rf(e,t,n){return typeof e=="string"?e:F.transform(t+n*e)}function l1(e,t,n){const r=rf(t,e.x,e.width),i=rf(n,e.y,e.height);return`${r} ${i}`}const u1={offset:"stroke-dashoffset",array:"stroke-dasharray"},c1={offset:"strokeDashoffset",array:"strokeDasharray"};function f1(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?u1:c1;e[s.offset]=F.transform(-r);const o=F.transform(t),a=F.transform(n);e[s.array]=`${o} ${a}`}function Su(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,h,m){if(xu(e,u,c,m),h){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:y,style:x,dimensions:w}=e;y.transform&&(w&&(x.transform=y.transform),delete y.transform),w&&(i!==void 0||s!==void 0||x.transform)&&(x.transformOrigin=l1(w,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(y.x=t),n!==void 0&&(y.y=n),r!==void 0&&(y.scale=r),o!==void 0&&f1(y,o,a,l,!1)}const Jp=()=>({...wu(),attrs:{}}),ku=e=>typeof e=="string"&&e.toLowerCase()==="svg";function d1(e,t,n,r){const i=C.useMemo(()=>{const s=Jp();return Su(s,t,{enableHardwareAcceleration:!1},ku(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Zp(s,e.style,e),i.style={...s,...i.style}}return i}function h1(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(vu(n)?d1:i1)(r,s,o,n),c={...a1(r,typeof n=="string",e),...l,ref:i},{children:h}=r,m=C.useMemo(()=>De(h)?h.get():h,[h]);return C.createElement(n,{...c,children:m})}}function em(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const tm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nm(e,t,n,r){em(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(tm.has(i)?i:mu(i),t.attrs[i])}function Cu(e,t){const{style:n}=e,r={};for(const i in n)(De(n[i])||t.style&&De(t.style[i])||Gp(i,e))&&(r[i]=n[i]);return r}function rm(e,t){const n=Cu(e,t);for(const r in e)if(De(e[r])||De(t[r])){const i=Ci.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Pu(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function p1(e){const t=C.useRef(null);return t.current===null&&(t.current=e()),t.current}const zs=e=>Array.isArray(e),m1=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),g1=e=>zs(e)?e[e.length-1]||0:e;function us(e){const t=De(e)?e.get():e;return m1(t)?t.toValue():t}function y1({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,s){const o={latestValues:v1(r,i,s,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const im=e=>(t,n)=>{const r=C.useContext(uo),i=C.useContext(pu),s=()=>y1(e,t,r,i);return n?s():p1(s)};function v1(e,t,n,r){const i={},s=r(e,{});for(const m in s)i[m]=us(s[m]);let{initial:o,animate:a}=e;const l=ho(e),u=Up(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const h=c?a:o;return h&&typeof h!="boolean"&&!fo(h)&&(Array.isArray(h)?h:[h]).forEach(y=>{const x=Pu(e,y);if(!x)return;const{transitionEnd:w,transition:P,...g}=x;for(const d in g){let f=g[d];if(Array.isArray(f)){const v=c?f.length-1:0;f=f[v]}f!==null&&(i[d]=f)}for(const d in w)i[d]=w[d]}),i}const re=e=>e;class sf{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function x1(e){let t=new sf,n=new sf,r=0,i=!1,s=!1;const o=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const h=c&&i,m=h?t:n;return u&&o.add(l),m.add(l)&&h&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(i){s=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),o.has(c)&&(a.schedule(c),e())}i=!1,s&&(s=!1,a.process(l))}};return a}const Gi=["prepare","read","update","preRender","render","postRender"],w1=40;function S1(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=Gi.reduce((h,m)=>(h[m]=x1(()=>n=!0),h),{}),o=h=>s[h].process(i),a=()=>{const h=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(h-i.timestamp,w1),1),i.timestamp=h,i.isProcessing=!0,Gi.forEach(o),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Gi.reduce((h,m)=>{const y=s[m];return h[m]=(x,w=!1,P=!1)=>(n||l(),y.schedule(x,w,P)),h},{}),cancel:h=>Gi.forEach(m=>s[m].cancel(h)),state:i,steps:s}}const{schedule:W,cancel:Dt,state:xe,steps:Go}=S1(typeof requestAnimationFrame<"u"?requestAnimationFrame:re,!0),k1={useVisualState:im({scrapeMotionValuesFromProps:rm,createRenderState:Jp,onMount:(e,t,{renderState:n,latestValues:r})=>{W.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),W.render(()=>{Su(n,r,{enableHardwareAcceleration:!1},ku(t.tagName),e.transformTemplate),nm(t,n)})}})},C1={useVisualState:im({scrapeMotionValuesFromProps:Cu,createRenderState:wu})};function P1(e,{forwardMotionProps:t=!1},n,r){return{...vu(e)?k1:C1,preloadedFeatures:n,useRender:h1(t),createVisualElement:r,Component:e}}function Ct(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const sm=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function mo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const E1=e=>t=>sm(t)&&e(t,mo(t));function Tt(e,t,n,r){return Ct(e,t,E1(n),r)}const T1=(e,t)=>n=>t(e(n)),tn=(...e)=>e.reduce(T1);function om(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const of=om("dragHorizontal"),af=om("dragVertical");function am(e){let t=!1;if(e==="y")t=af();else if(e==="x")t=of();else{const n=of(),r=af();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function lm(){const e=am(!0);return e?(e(),!1):!0}class cn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function lf(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(s,o)=>{if(s.pointerType==="touch"||lm())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&W.update(()=>a[r](s,o))};return Tt(e.current,n,i,{passive:!e.getProps()[r]})}class j1 extends cn{mount(){this.unmount=tn(lf(this.node,!0),lf(this.node,!1))}unmount(){}}class N1 extends cn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tn(Ct(this.node.current,"focus",()=>this.onFocus()),Ct(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const um=(e,t)=>t?e===t?!0:um(e,t.parentElement):!1;function Ko(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,mo(n))}class A1 extends cn{constructor(){super(...arguments),this.removeStartListeners=re,this.removeEndListeners=re,this.removeAccessibleListeners=re,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),s=Tt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:h}=this.node.getProps();W.update(()=>{!h&&!um(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),o=Tt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tn(s,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=s=>{if(s.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||Ko("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&W.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Ct(this.node.current,"keyup",o),Ko("down",(a,l)=>{this.startPress(a,l)})},n=Ct(this.node.current,"keydown",t),r=()=>{this.isPressing&&Ko("cancel",(s,o)=>this.cancelPress(s,o))},i=Ct(this.node.current,"blur",r);this.removeAccessibleListeners=tn(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&W.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!lm()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&W.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Tt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Ct(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const tl=new WeakMap,Qo=new WeakMap,L1=e=>{const t=tl.get(e.target);t&&t(e)},R1=e=>{e.forEach(L1)};function M1({root:e,...t}){const n=e||document;Qo.has(n)||Qo.set(n,{});const r=Qo.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(R1,{root:e,...t})),r[i]}function D1(e,t,n){const r=M1(t);return tl.set(e,n),r.observe(e),()=>{tl.delete(e),r.unobserve(e)}}const F1={some:0,all:1};class _1 extends cn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:F1[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),m=u?c:h;m&&m(l)};return D1(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(V1(t,n))&&this.startObserver()}unmount(){}}function V1({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const O1={inView:{Feature:_1},tap:{Feature:A1},focus:{Feature:N1},hover:{Feature:j1}};function cm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function I1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function z1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function go(e,t,n){const r=e.getProps();return Pu(r,t,n!==void 0?n:r.custom,I1(e),z1(e))}let Eu=re;const kn=e=>e*1e3,jt=e=>e/1e3,b1={current:!1},fm=e=>Array.isArray(e)&&typeof e[0]=="number";function dm(e){return!!(!e||typeof e=="string"&&hm[e]||fm(e)||Array.isArray(e)&&e.every(dm))}const Dr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,hm={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Dr([0,.65,.55,1]),circOut:Dr([.55,0,1,.45]),backIn:Dr([.31,.01,.66,-.59]),backOut:Dr([.33,1.53,.69,.99])};function pm(e){if(e)return fm(e)?Dr(e):Array.isArray(e)?e.map(pm):hm[e]}function B1(e,t,n,{delay:r=0,duration:i,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=pm(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}function $1(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const mm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,U1=1e-7,H1=12;function W1(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=mm(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>U1&&++a<H1);return o}function Ti(e,t,n,r){if(e===t&&n===r)return re;const i=s=>W1(s,0,1,e,n);return s=>s===0||s===1?s:mm(i(s),t,r)}const G1=Ti(.42,0,1,1),K1=Ti(0,0,.58,1),gm=Ti(.42,0,.58,1),Q1=e=>Array.isArray(e)&&typeof e[0]!="number",ym=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,vm=e=>t=>1-e(1-t),Tu=e=>1-Math.sin(Math.acos(e)),xm=vm(Tu),Y1=ym(Tu),wm=Ti(.33,1.53,.69,.99),ju=vm(wm),X1=ym(ju),Z1=e=>(e*=2)<1?.5*ju(e):.5*(2-Math.pow(2,-10*(e-1))),q1={linear:re,easeIn:G1,easeInOut:gm,easeOut:K1,circIn:Tu,circInOut:Y1,circOut:xm,backIn:ju,backInOut:X1,backOut:wm,anticipate:Z1},uf=e=>{if(Array.isArray(e)){Eu(e.length===4);const[t,n,r,i]=e;return Ti(t,n,r,i)}else if(typeof e=="string")return q1[e];return e},Nu=(e,t)=>n=>!!(Pi(n)&&Jx.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Sm=(e,t,n)=>r=>{if(!Pi(r))return r;const[i,s,o,a]=r.match(po);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},J1=e=>sn(0,255,e),Yo={...Rn,transform:e=>Math.round(J1(e))},xn={test:Nu("rgb","red"),parse:Sm("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Yo.transform(e)+", "+Yo.transform(t)+", "+Yo.transform(n)+", "+Wr(Hr.transform(r))+")"};function ew(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const nl={test:Nu("#"),parse:ew,transform:xn.transform},Kn={test:Nu("hsl","hue"),parse:Sm("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+mt.transform(Wr(t))+", "+mt.transform(Wr(n))+", "+Wr(Hr.transform(r))+")"},Ce={test:e=>xn.test(e)||nl.test(e)||Kn.test(e),parse:e=>xn.test(e)?xn.parse(e):Kn.test(e)?Kn.parse(e):nl.parse(e),transform:e=>Pi(e)?e:e.hasOwnProperty("red")?xn.transform(e):Kn.transform(e)},X=(e,t,n)=>-n*e+n*t+e;function Xo(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function tw({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Xo(l,a,e+1/3),s=Xo(l,a,e),o=Xo(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}const Zo=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},nw=[nl,xn,Kn],rw=e=>nw.find(t=>t.test(e));function cf(e){const t=rw(e);let n=t.parse(e);return t===Kn&&(n=tw(n)),n}const km=(e,t)=>{const n=cf(e),r=cf(t),i={...n};return s=>(i.red=Zo(n.red,r.red,s),i.green=Zo(n.green,r.green,s),i.blue=Zo(n.blue,r.blue,s),i.alpha=X(n.alpha,r.alpha,s),xn.transform(i))};function iw(e){var t,n;return isNaN(e)&&Pi(e)&&(((t=e.match(po))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Yp))===null||n===void 0?void 0:n.length)||0)>0}const Cm={regex:Zx,countKey:"Vars",token:"${v}",parse:re},Pm={regex:Yp,countKey:"Colors",token:"${c}",parse:Ce.parse},Em={regex:po,countKey:"Numbers",token:"${n}",parse:Rn.parse};function qo(e,{regex:t,countKey:n,token:r,parse:i}){const s=e.tokenised.match(t);s&&(e["num"+n]=s.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...s.map(i)))}function bs(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&qo(n,Cm),qo(n,Pm),qo(n,Em),n}function Tm(e){return bs(e).values}function jm(e){const{values:t,numColors:n,numVars:r,tokenised:i}=bs(e),s=t.length;return o=>{let a=i;for(let l=0;l<s;l++)l<r?a=a.replace(Cm.token,o[l]):l<r+n?a=a.replace(Pm.token,Ce.transform(o[l])):a=a.replace(Em.token,Wr(o[l]));return a}}const sw=e=>typeof e=="number"?0:e;function ow(e){const t=Tm(e);return jm(e)(t.map(sw))}const on={test:iw,parse:Tm,createTransformer:jm,getAnimatableNone:ow},Nm=(e,t)=>n=>`${n>0?t:e}`;function Am(e,t){return typeof e=="number"?n=>X(e,t,n):Ce.test(e)?km(e,t):e.startsWith("var(")?Nm(e,t):Rm(e,t)}const Lm=(e,t)=>{const n=[...e],r=n.length,i=e.map((s,o)=>Am(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}},aw=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Am(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}},Rm=(e,t)=>{const n=on.createTransformer(t),r=bs(e),i=bs(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?tn(Lm(r.values,i.values),n):Nm(e,t)},pi=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},ff=(e,t)=>n=>X(e,t,n);function lw(e){return typeof e=="number"?ff:typeof e=="string"?Ce.test(e)?km:Rm:Array.isArray(e)?Lm:typeof e=="object"?aw:ff}function uw(e,t,n){const r=[],i=n||lw(e[0]),s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||re:t;a=tn(l,a)}r.push(a)}return r}function Mm(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(Eu(s===t.length),s===1)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=uw(t,r,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const h=pi(e[c],e[c+1],u);return o[c](h)};return n?u=>l(sn(e[0],e[s-1],u)):l}function cw(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=pi(0,t,r);e.push(X(n,1,i))}}function fw(e){const t=[0];return cw(t,e.length-1),t}function dw(e,t){return e.map(n=>n*t)}function hw(e,t){return e.map(()=>t||gm).splice(0,e.length-1)}function Bs({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Q1(r)?r.map(uf):uf(r),s={done:!1,value:t[0]},o=dw(n&&n.length===t.length?n:fw(t),e),a=Mm(o,t,{ease:Array.isArray(i)?i:hw(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}function Dm(e,t){return t?e*(1e3/t):0}const pw=5;function Fm(e,t,n){const r=Math.max(t-pw,0);return Dm(n-e(r),t-r)}const df=.001,mw=.01,gw=10,yw=.05,vw=1;function xw({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,s,o=1-t;o=sn(yw,vw,o),e=sn(mw,gw,jt(e)),o<1?(i=u=>{const c=u*o,h=c*e,m=c-n,y=rl(u,o),x=Math.exp(-h);return df-m/y*x},s=u=>{const h=u*o*e,m=h*n+n,y=Math.pow(o,2)*Math.pow(u,2)*e,x=Math.exp(-h),w=rl(Math.pow(u,2),o);return(-i(u)+df>0?-1:1)*((m-y)*x)/w}):(i=u=>{const c=Math.exp(-u*e),h=(u-n)*e+1;return-.001+c*h},s=u=>{const c=Math.exp(-u*e),h=(n-u)*(e*e);return c*h});const a=5/e,l=Sw(i,s,a);if(e=kn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const ww=12;function Sw(e,t,n){let r=n;for(let i=1;i<ww;i++)r=r-e(r)/t(r);return r}function rl(e,t){return e*Math.sqrt(1-t*t)}const kw=["duration","bounce"],Cw=["stiffness","damping","mass"];function hf(e,t){return t.some(n=>e[n]!==void 0)}function Pw(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!hf(e,Cw)&&hf(e,kw)){const n=xw(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function _m({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],s=e[e.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:h,isResolvedFromDuration:m}=Pw({...r,velocity:-jt(r.velocity||0)}),y=h||0,x=l/(2*Math.sqrt(a*u)),w=s-i,P=jt(Math.sqrt(a/u)),g=Math.abs(w)<5;n||(n=g?.01:2),t||(t=g?.005:.5);let d;if(x<1){const f=rl(P,x);d=v=>{const S=Math.exp(-x*P*v);return s-S*((y+x*P*w)/f*Math.sin(f*v)+w*Math.cos(f*v))}}else if(x===1)d=f=>s-Math.exp(-P*f)*(w+(y+P*w)*f);else{const f=P*Math.sqrt(x*x-1);d=v=>{const S=Math.exp(-x*P*v),k=Math.min(f*v,300);return s-S*((y+x*P*w)*Math.sinh(k)+f*w*Math.cosh(k))/f}}return{calculatedDuration:m&&c||null,next:f=>{const v=d(f);if(m)o.done=f>=c;else{let S=y;f!==0&&(x<1?S=Fm(d,f,v):S=0);const k=Math.abs(S)<=n,E=Math.abs(s-v)<=t;o.done=k&&E}return o.value=o.done?s:v,o}}}function pf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=e[0],m={done:!1,value:h},y=T=>a!==void 0&&T<a||l!==void 0&&T>l,x=T=>a===void 0?l:l===void 0||Math.abs(a-T)<Math.abs(l-T)?a:l;let w=n*t;const P=h+w,g=o===void 0?P:o(P);g!==P&&(w=g-h);const d=T=>-w*Math.exp(-T/r),f=T=>g+d(T),v=T=>{const j=d(T),L=f(T);m.done=Math.abs(j)<=u,m.value=m.done?g:L};let S,k;const E=T=>{y(m.value)&&(S=T,k=_m({keyframes:[m.value,x(m.value)],velocity:Fm(f,T,m.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:T=>{let j=!1;return!k&&S===void 0&&(j=!0,v(T),E(T)),S!==void 0&&T>S?k.next(T-S):(!j&&v(T),m)}}}const Ew=e=>{const t=({timestamp:n})=>e(n);return{start:()=>W.update(t,!0),stop:()=>Dt(t),now:()=>xe.isProcessing?xe.timestamp:performance.now()}},mf=2e4;function gf(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<mf;)t+=n,r=e.next(t);return t>=mf?1/0:t}const Tw={decay:pf,inertia:pf,tween:Bs,keyframes:Bs,spring:_m};function $s({autoplay:e=!0,delay:t=0,driver:n=Ew,keyframes:r,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:h,...m}){let y=1,x=!1,w,P;const g=()=>{P=new Promise(D=>{w=D})};g();let d;const f=Tw[i]||Bs;let v;f!==Bs&&typeof r[0]!="number"&&(v=Mm([0,100],r,{clamp:!1}),r=[0,100]);const S=f({...m,keyframes:r});let k;a==="mirror"&&(k=f({...m,keyframes:[...r].reverse(),velocity:-(m.velocity||0)}));let E="idle",T=null,j=null,L=null;S.calculatedDuration===null&&s&&(S.calculatedDuration=gf(S));const{calculatedDuration:V}=S;let b=1/0,ue=1/0;V!==null&&(b=V+o,ue=b*(s+1)-o);let te=0;const lt=D=>{if(j===null)return;y>0&&(j=Math.min(j,D)),y<0&&(j=Math.min(D-ue/y,j)),T!==null?te=T:te=Math.round(D-j)*y;const I=te-t*(y>=0?1:-1),vt=y>=0?I<0:I>ue;te=Math.max(I,0),E==="finished"&&T===null&&(te=ue);let oe=te,Be=S;if(s){const Mn=Math.min(te,ue)/b;let Ot=Math.floor(Mn),Ue=Mn%1;!Ue&&Mn>=1&&(Ue=1),Ue===1&&Ot--,Ot=Math.min(Ot,s+1),!!(Ot%2)&&(a==="reverse"?(Ue=1-Ue,o&&(Ue-=o/b)):a==="mirror"&&(Be=k)),oe=sn(0,1,Ue)*b}const ce=vt?{done:!1,value:r[0]}:Be.next(oe);v&&(ce.value=v(ce.value));let{done:$e}=ce;!vt&&V!==null&&($e=y>=0?te>=ue:te<=0);const gr=T===null&&(E==="finished"||E==="running"&&$e);return h&&h(ce.value),gr&&A(),ce},Q=()=>{d&&d.stop(),d=void 0},ke=()=>{E="idle",Q(),w(),g(),j=L=null},A=()=>{E="finished",c&&c(),Q(),w()},M=()=>{if(x)return;d||(d=n(lt));const D=d.now();l&&l(),T!==null?j=D-T:(!j||E==="finished")&&(j=D),E==="finished"&&g(),L=j,T=null,E="running",d.start()};e&&M();const _={then(D,I){return P.then(D,I)},get time(){return jt(te)},set time(D){D=kn(D),te=D,T!==null||!d||y===0?T=D:j=d.now()-D/y},get duration(){const D=S.calculatedDuration===null?gf(S):S.calculatedDuration;return jt(D)},get speed(){return y},set speed(D){D===y||!d||(y=D,_.time=jt(te))},get state(){return E},play:M,pause:()=>{E="paused",T=te},stop:()=>{x=!0,E!=="idle"&&(E="idle",u&&u(),ke())},cancel:()=>{L!==null&&lt(L),ke()},complete:()=>{E="finished"},sample:D=>(j=0,lt(D))};return _}function jw(e){let t;return()=>(t===void 0&&(t=e()),t)}const Nw=jw(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Aw=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ki=10,Lw=2e4,Rw=(e,t)=>t.type==="spring"||e==="backgroundColor"||!dm(t.ease);function Mw(e,t,{onUpdate:n,onComplete:r,...i}){if(!(Nw()&&Aw.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,l,u=!1;const c=()=>{l=new Promise(f=>{a=f})};c();let{keyframes:h,duration:m=300,ease:y,times:x}=i;if(Rw(t,i)){const f=$s({...i,repeat:0,delay:0});let v={done:!1,value:h[0]};const S=[];let k=0;for(;!v.done&&k<Lw;)v=f.sample(k),S.push(v.value),k+=Ki;x=void 0,h=S,m=k-Ki,y="linear"}const w=B1(e.owner.current,t,h,{...i,duration:m,ease:y,times:x}),P=()=>{u=!1,w.cancel()},g=()=>{u=!0,W.update(P),a(),c()};return w.onfinish=()=>{u||(e.set($1(h,i)),r&&r(),g())},{then(f,v){return l.then(f,v)},attachTimeline(f){return w.timeline=f,w.onfinish=null,re},get time(){return jt(w.currentTime||0)},set time(f){w.currentTime=kn(f)},get speed(){return w.playbackRate},set speed(f){w.playbackRate=f},get duration(){return jt(m)},play:()=>{o||(w.play(),Dt(P))},pause:()=>w.pause(),stop:()=>{if(o=!0,w.playState==="idle")return;const{currentTime:f}=w;if(f){const v=$s({...i,autoplay:!1});e.setWithVelocity(v.sample(f-Ki).value,v.sample(f).value,Ki)}g()},complete:()=>{u||w.finish()},cancel:g}}function Dw({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:re,pause:re,stop:re,then:s=>(s(),Promise.resolve()),cancel:re,complete:re});return t?$s({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const Fw={type:"spring",stiffness:500,damping:25,restSpeed:10},_w=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Vw={type:"keyframes",duration:.8},Ow={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Iw=(e,{keyframes:t})=>t.length>2?Vw:Ln.has(e)?e.startsWith("scale")?_w(t[1]):Fw:Ow,il=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(on.test(t)||t==="0")&&!t.startsWith("url(")),zw=new Set(["brightness","contrast","saturate","opacity"]);function bw(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(po)||[];if(!r)return e;const i=n.replace(r,"");let s=zw.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Bw=/([a-z-]*)\(.*?\)/g,sl={...on,getAnimatableNone:e=>{const t=e.match(Bw);return t?t.map(bw).join(" "):e}},$w={...Xp,color:Ce,backgroundColor:Ce,outlineColor:Ce,fill:Ce,stroke:Ce,borderColor:Ce,borderTopColor:Ce,borderRightColor:Ce,borderBottomColor:Ce,borderLeftColor:Ce,filter:sl,WebkitFilter:sl},Au=e=>$w[e];function Vm(e,t){let n=Au(e);return n!==sl&&(n=on),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Om=e=>/^0[^.\s]+$/.test(e);function Uw(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Om(e)}function Hw(e,t,n,r){const i=il(t,n);let s;Array.isArray(n)?s=[...n]:s=[null,n];const o=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<s.length;u++)s[u]===null&&(s[u]=u===0?o:s[u-1]),Uw(s[u])&&l.push(u),typeof s[u]=="string"&&s[u]!=="none"&&s[u]!=="0"&&(a=s[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];s[c]=Vm(t,a)}return s}function Ww({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function Lu(e,t){return e[t]||e.default||e}const Gw={skipAnimations:!1},Ru=(e,t,n,r={})=>i=>{const s=Lu(r,e)||{},o=s.delay||r.delay||0;let{elapsed:a=0}=r;a=a-kn(o);const l=Hw(t,e,n,s),u=l[0],c=l[l.length-1],h=il(e,u),m=il(e,c);let y={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:x=>{t.set(x),s.onUpdate&&s.onUpdate(x)},onComplete:()=>{i(),s.onComplete&&s.onComplete()}};if(Ww(s)||(y={...y,...Iw(e,y)}),y.duration&&(y.duration=kn(y.duration)),y.repeatDelay&&(y.repeatDelay=kn(y.repeatDelay)),!h||!m||b1.current||s.type===!1||Gw.skipAnimations)return Dw(y);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const x=Mw(t,e,y);if(x)return x}return $s(y)};function Us(e){return!!(De(e)&&e.add)}const Im=e=>/^\-?\d*\.?\d+$/.test(e);function Mu(e,t){e.indexOf(t)===-1&&e.push(t)}function Du(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Fu{constructor(){this.subscriptions=[]}add(t){return Mu(this.subscriptions,t),()=>Du(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Kw=e=>!isNaN(parseFloat(e));class Qw{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:s,timestamp:o}=xe;this.lastUpdated!==o&&(this.timeDelta=s,this.lastUpdated=o,W.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>W.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Kw(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Fu);const r=this.events[t].add(n);return t==="change"?()=>{r(),W.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Dm(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ur(e,t){return new Qw(e,t)}const zm=e=>t=>t.test(e),Yw={test:e=>e==="auto",parse:e=>e},bm=[Rn,F,mt,zt,t1,e1,Yw],Tr=e=>bm.find(zm(e)),Xw=[...bm,Ce,on],Zw=e=>Xw.find(zm(e));function qw(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ur(n))}function Jw(e,t){const n=go(e,t);let{transitionEnd:r={},transition:i={},...s}=n?e.makeTargetAnimatable(n,!1):{};s={...s,...r};for(const o in s){const a=g1(s[o]);qw(e,o,a)}}function eS(e,t,n){var r,i;const s=Object.keys(t).filter(a=>!e.hasValue(a)),o=s.length;if(o)for(let a=0;a<o;a++){const l=s[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(Im(c)||Om(c))?c=parseFloat(c):!Zw(c)&&on.test(u)&&(c=Vm(l,u)),e.addValue(l,ur(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function tS(e,t){return t?(t[e]||t.default||t).from:void 0}function nS(e,t,n){const r={};for(const i in e){const s=tS(i,t);if(s!==void 0)r[i]=s;else{const o=n.getValue(i);o&&(r[i]=o.get())}}return r}function rS({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function iS(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Bm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const h in a){const m=e.getValue(h),y=a[h];if(!m||y===void 0||c&&rS(c,h))continue;const x={delay:n,elapsed:0,...Lu(s||{},h)};if(window.HandoffAppearAnimations){const g=e.getProps()[$p];if(g){const d=window.HandoffAppearAnimations(g,h,m,W);d!==null&&(x.elapsed=d,x.isHandoff=!0)}}let w=!x.isHandoff&&!iS(m,y);if(x.type==="spring"&&(m.getVelocity()||x.velocity)&&(w=!1),m.animation&&(w=!1),w)continue;m.start(Ru(h,m,y,e.shouldReduceMotion&&Ln.has(h)?{type:!1}:x));const P=m.animation;Us(l)&&(l.add(h),P.then(()=>l.remove(h))),u.push(P)}return o&&Promise.all(u).then(()=>{o&&Jw(e,o)}),u}function ol(e,t,n={}){const r=go(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const s=r?()=>Promise.all(Bm(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:h}=i;return sS(e,t,u+l,c,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[s,o]:[o,s];return l().then(()=>u())}else return Promise.all([s(),o(n.delay)])}function sS(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(oS).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(ol(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function oS(e,t){return e.sortNodePosition(t)}function aS(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>ol(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=ol(e,t,n);else{const i=typeof t=="function"?go(e,t,n.custom):t;r=Promise.all(Bm(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const lS=[...gu].reverse(),uS=gu.length;function cS(e){return t=>Promise.all(t.map(({animation:n,options:r})=>aS(e,n,r)))}function fS(e){let t=cS(e);const n=hS();let r=!0;const i=(l,u)=>{const c=go(e,u);if(c){const{transition:h,transitionEnd:m,...y}=c;l={...l,...y,...m}}return l};function s(l){t=l(e)}function o(l,u){const c=e.getProps(),h=e.getVariantContext(!0)||{},m=[],y=new Set;let x={},w=1/0;for(let g=0;g<uS;g++){const d=lS[g],f=n[d],v=c[d]!==void 0?c[d]:h[d],S=di(v),k=d===u?f.isActive:null;k===!1&&(w=g);let E=v===h[d]&&v!==c[d]&&S;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),f.protectedKeys={...x},!f.isActive&&k===null||!v&&!f.prevProp||fo(v)||typeof v=="boolean")continue;let j=dS(f.prevProp,v)||d===u&&f.isActive&&!E&&S||g>w&&S,L=!1;const V=Array.isArray(v)?v:[v];let b=V.reduce(i,{});k===!1&&(b={});const{prevResolvedValues:ue={}}=f,te={...ue,...b},lt=Q=>{j=!0,y.has(Q)&&(L=!0,y.delete(Q)),f.needsAnimating[Q]=!0};for(const Q in te){const ke=b[Q],A=ue[Q];if(x.hasOwnProperty(Q))continue;let M=!1;zs(ke)&&zs(A)?M=!cm(ke,A):M=ke!==A,M?ke!==void 0?lt(Q):y.add(Q):ke!==void 0&&y.has(Q)?lt(Q):f.protectedKeys[Q]=!0}f.prevProp=v,f.prevResolvedValues=b,f.isActive&&(x={...x,...b}),r&&e.blockInitialAnimation&&(j=!1),j&&(!E||L)&&m.push(...V.map(Q=>({animation:Q,options:{type:d,...l}})))}if(y.size){const g={};y.forEach(d=>{const f=e.getBaseTarget(d);f!==void 0&&(g[d]=f)}),m.push({animation:g})}let P=!!m.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(P=!1),r=!1,P?t(m):Promise.resolve()}function a(l,u,c){var h;if(n[l].isActive===u)return Promise.resolve();(h=e.variantChildren)===null||h===void 0||h.forEach(y=>{var x;return(x=y.animationState)===null||x===void 0?void 0:x.setActive(l,u)}),n[l].isActive=u;const m=o(c,l);for(const y in n)n[y].protectedKeys={};return m}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n}}function dS(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!cm(t,e):!1}function fn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function hS(){return{animate:fn(!0),whileInView:fn(),whileHover:fn(),whileTap:fn(),whileDrag:fn(),whileFocus:fn(),exit:fn()}}class pS extends cn{constructor(t){super(t),t.animationState||(t.animationState=fS(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),fo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let mS=0;class gS extends cn{constructor(){super(...arguments),this.id=mS++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const yS={animation:{Feature:pS},exit:{Feature:gS}},yf=(e,t)=>Math.abs(e-t);function vS(e,t){const n=yf(e.x,t.x),r=yf(e.y,t.y);return Math.sqrt(n**2+r**2)}class $m{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ea(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,y=vS(h.offset,{x:0,y:0})>=3;if(!m&&!y)return;const{point:x}=h,{timestamp:w}=xe;this.history.push({...x,timestamp:w});const{onStart:P,onMove:g}=this.handlers;m||(P&&P(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,h)},this.handlePointerMove=(h,m)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=Jo(m,this.transformPagePoint),W.update(this.updatePoint,!0)},this.handlePointerUp=(h,m)=>{this.end();const{onEnd:y,onSessionEnd:x,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const P=ea(h.type==="pointercancel"?this.lastMoveEventInfo:Jo(m,this.transformPagePoint),this.history);this.startEvent&&y&&y(h,P),x&&x(h,P)},!sm(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=mo(t),a=Jo(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=xe;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ea(a,this.history)),this.removeListeners=tn(Tt(this.contextWindow,"pointermove",this.handlePointerMove),Tt(this.contextWindow,"pointerup",this.handlePointerUp),Tt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Dt(this.updatePoint)}}function Jo(e,t){return t?{point:t(e.point)}:e}function vf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ea({point:e},t){return{point:e,delta:vf(e,Um(t)),offset:vf(e,xS(t)),velocity:wS(t,.1)}}function xS(e){return e[0]}function Um(e){return e[e.length-1]}function wS(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Um(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>kn(t)));)n--;if(!r)return{x:0,y:0};const s=jt(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ie(e){return e.max-e.min}function al(e,t=0,n=.01){return Math.abs(e-t)<=n}function xf(e,t,n,r=.5){e.origin=r,e.originPoint=X(t.min,t.max,e.origin),e.scale=Ie(n)/Ie(t),(al(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=X(n.min,n.max,e.origin)-e.originPoint,(al(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Gr(e,t,n,r){xf(e.x,t.x,n.x,r?r.originX:void 0),xf(e.y,t.y,n.y,r?r.originY:void 0)}function wf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ie(t)}function SS(e,t,n){wf(e.x,t.x,n.x),wf(e.y,t.y,n.y)}function Sf(e,t,n){e.min=t.min-n.min,e.max=e.min+Ie(t)}function Kr(e,t,n){Sf(e.x,t.x,n.x),Sf(e.y,t.y,n.y)}function kS(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?X(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?X(n,e,r.max):Math.min(e,n)),e}function kf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function CS(e,{top:t,left:n,bottom:r,right:i}){return{x:kf(e.x,n,i),y:kf(e.y,t,r)}}function Cf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function PS(e,t){return{x:Cf(e.x,t.x),y:Cf(e.y,t.y)}}function ES(e,t){let n=.5;const r=Ie(e),i=Ie(t);return i>r?n=pi(t.min,t.max-r,e.min):r>i&&(n=pi(e.min,e.max-i,t.min)),sn(0,1,n)}function TS(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const ll=.35;function jS(e=ll){return e===!1?e=0:e===!0&&(e=ll),{x:Pf(e,"left","right"),y:Pf(e,"top","bottom")}}function Pf(e,t,n){return{min:Ef(e,t),max:Ef(e,n)}}function Ef(e,t){return typeof e=="number"?e:e[t]||0}const Tf=()=>({translate:0,scale:1,origin:0,originPoint:0}),Qn=()=>({x:Tf(),y:Tf()}),jf=()=>({min:0,max:0}),ie=()=>({x:jf(),y:jf()});function We(e){return[e("x"),e("y")]}function Hm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function NS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function AS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ta(e){return e===void 0||e===1}function ul({scale:e,scaleX:t,scaleY:n}){return!ta(e)||!ta(t)||!ta(n)}function pn(e){return ul(e)||Wm(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Wm(e){return Nf(e.x)||Nf(e.y)}function Nf(e){return e&&e!=="0%"}function Hs(e,t,n){const r=e-n,i=t*r;return n+i}function Af(e,t,n,r,i){return i!==void 0&&(e=Hs(e,i,r)),Hs(e,n,r)+t}function cl(e,t=0,n=1,r,i){e.min=Af(e.min,t,n,r,i),e.max=Af(e.max,t,n,r,i)}function Gm(e,{x:t,y:n}){cl(e.x,t.translate,t.scale,t.originPoint),cl(e.y,n.translate,n.scale,n.originPoint)}function LS(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const l=s.instance;l&&l.style&&l.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Yn(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Gm(e,o)),r&&pn(s.latestValues)&&Yn(e,s.latestValues))}t.x=Lf(t.x),t.y=Lf(t.y)}function Lf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function $t(e,t){e.min=e.min+t,e.max=e.max+t}function Rf(e,t,[n,r,i]){const s=t[i]!==void 0?t[i]:.5,o=X(e.min,e.max,s);cl(e,t[n],t[r],o,t.scale)}const RS=["x","scaleX","originX"],MS=["y","scaleY","originY"];function Yn(e,t){Rf(e.x,t,RS),Rf(e.y,t,MS)}function Km(e,t){return Hm(AS(e.getBoundingClientRect(),t))}function DS(e,t,n){const r=Km(e,n),{scroll:i}=t;return i&&($t(r.x,i.offset.x),$t(r.y,i.offset.y)),r}const Qm=({current:e})=>e?e.ownerDocument.defaultView:null,FS=new WeakMap;class _S{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ie(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(mo(c,"page").point)},s=(c,h)=>{const{drag:m,dragPropagation:y,onDragStart:x}=this.getProps();if(m&&!y&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=am(m),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),We(P=>{let g=this.getAxisMotionValue(P).get()||0;if(mt.test(g)){const{projection:d}=this.visualElement;if(d&&d.layout){const f=d.layout.layoutBox[P];f&&(g=Ie(f)*(parseFloat(g)/100))}}this.originPoint[P]=g}),x&&W.update(()=>x(c,h),!1,!0);const{animationState:w}=this.visualElement;w&&w.setActive("whileDrag",!0)},o=(c,h)=>{const{dragPropagation:m,dragDirectionLock:y,onDirectionLock:x,onDrag:w}=this.getProps();if(!m&&!this.openGlobalLock)return;const{offset:P}=h;if(y&&this.currentDirection===null){this.currentDirection=VS(P),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",h.point,P),this.updateAxis("y",h.point,P),this.visualElement.render(),w&&w(c,h)},a=(c,h)=>this.stop(c,h),l=()=>We(c=>{var h;return this.getAnimationState(c)==="paused"&&((h=this.getAxisMotionValue(c).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new $m(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Qm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&W.update(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Qi(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=kS(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&Gn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=CS(i.layoutBox,n):this.constraints=!1,this.elastic=jS(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&We(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=TS(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Gn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=DS(r,i.root,this.visualElement.getTransformPagePoint());let o=PS(i.layout.layoutBox,s);if(n){const a=n(NS(o));this.hasMutatedConstraints=!!a,a&&(o=Hm(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=We(c=>{if(!Qi(c,n,this.currentDirection))return;let h=l&&l[c]||{};o&&(h={min:0,max:0});const m=i?200:1e6,y=i?40:1e7,x={type:"inertia",velocity:r?t[c]:0,bounceStiffness:m,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...s,...h};return this.startAxisValueAnimation(c,x)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Ru(t,r,0,n))}stopAnimation(){We(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){We(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){We(n=>{const{drag:r}=this.getProps();if(!Qi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-X(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Gn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};We(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();i[o]=ES({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),We(o=>{if(!Qi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(X(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;FS.set(this.visualElement,this);const t=this.visualElement.current,n=Tt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Gn(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const o=Ct(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(We(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=ll,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function Qi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function VS(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class OS extends cn{constructor(t){super(t),this.removeGroupControls=re,this.removeListeners=re,this.controls=new _S(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||re}unmount(){this.removeGroupControls(),this.removeListeners()}}const Mf=e=>(t,n)=>{e&&W.update(()=>e(t,n))};class IS extends cn{constructor(){super(...arguments),this.removePointerDownListener=re}onPointerDown(t){this.session=new $m(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Qm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Mf(t),onStart:Mf(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&W.update(()=>i(s,o))}}}mount(){this.removePointerDownListener=Tt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function zS(){const e=C.useContext(pu);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=C.useId();return C.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const cs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Df(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const jr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(F.test(e))e=parseFloat(e);else return e;const n=Df(e,t.target.x),r=Df(e,t.target.y);return`${n}% ${r}%`}},bS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=on.parse(e);if(i.length>5)return r;const s=on.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=X(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class BS extends yl.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;Kx($S),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),cs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||W.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Ym(e){const[t,n]=zS(),r=C.useContext(Hp);return yl.createElement(BS,{...e,layoutGroup:r,switchLayoutGroup:C.useContext(Wp),isPresent:t,safeToRemove:n})}const $S={borderRadius:{...jr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:jr,borderTopRightRadius:jr,borderBottomLeftRadius:jr,borderBottomRightRadius:jr,boxShadow:bS},Xm=["TopLeft","TopRight","BottomLeft","BottomRight"],US=Xm.length,Ff=e=>typeof e=="string"?parseFloat(e):e,_f=e=>typeof e=="number"||F.test(e);function HS(e,t,n,r,i,s){i?(e.opacity=X(0,n.opacity!==void 0?n.opacity:1,WS(r)),e.opacityExit=X(t.opacity!==void 0?t.opacity:1,0,GS(r))):s&&(e.opacity=X(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<US;o++){const a=`border${Xm[o]}Radius`;let l=Vf(t,a),u=Vf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||_f(l)===_f(u)?(e[a]=Math.max(X(Ff(l),Ff(u),r),0),(mt.test(u)||mt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=X(t.rotate||0,n.rotate||0,r))}function Vf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const WS=Zm(0,.5,xm),GS=Zm(.5,.95,re);function Zm(e,t,n){return r=>r<e?0:r>t?1:n(pi(e,t,r))}function Of(e,t){e.min=t.min,e.max=t.max}function He(e,t){Of(e.x,t.x),Of(e.y,t.y)}function If(e,t,n,r,i){return e-=t,e=Hs(e,1/n,r),i!==void 0&&(e=Hs(e,1/i,r)),e}function KS(e,t=0,n=1,r=.5,i,s=e,o=e){if(mt.test(t)&&(t=parseFloat(t),t=X(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=X(s.min,s.max,r);e===s&&(a-=t),e.min=If(e.min,t,n,a,i),e.max=If(e.max,t,n,a,i)}function zf(e,t,[n,r,i],s,o){KS(e,t[n],t[r],t[i],t.scale,s,o)}const QS=["x","scaleX","originX"],YS=["y","scaleY","originY"];function bf(e,t,n,r){zf(e.x,t,QS,n?n.x:void 0,r?r.x:void 0),zf(e.y,t,YS,n?n.y:void 0,r?r.y:void 0)}function Bf(e){return e.translate===0&&e.scale===1}function qm(e){return Bf(e.x)&&Bf(e.y)}function XS(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Jm(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function $f(e){return Ie(e.x)/Ie(e.y)}class ZS{constructor(){this.members=[]}add(t){Mu(this.members,t),t.scheduleRender()}remove(t){if(Du(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Uf(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y;if((i||s)&&(r=`translate3d(${i}px, ${s}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const o=e.x.scale*t.x,a=e.y.scale*t.y;return(o!==1||a!==1)&&(r+=`scale(${o}, ${a})`),r||"none"}const qS=(e,t)=>e.depth-t.depth;class JS{constructor(){this.children=[],this.isDirty=!1}add(t){Mu(this.children,t),this.isDirty=!0}remove(t){Du(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(qS),this.isDirty=!1,this.children.forEach(t)}}function ek(e,t){const n=performance.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(Dt(r),e(s-t))};return W.read(r,!0),()=>Dt(r)}function tk(e){window.MotionDebug&&window.MotionDebug.record(e)}function nk(e){return e instanceof SVGElement&&e.tagName!=="svg"}function rk(e,t,n){const r=De(e)?e:ur(e);return r.start(Ru("",r,t,n)),r.animation}const Hf=["","X","Y","Z"],ik={visibility:"hidden"},Wf=1e3;let sk=0;const mn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function eg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=sk++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,mn.totalNodes=mn.resolvedTargetDeltas=mn.recalculatedProjection=0,this.nodes.forEach(lk),this.nodes.forEach(hk),this.nodes.forEach(pk),this.nodes.forEach(uk),tk(mn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new JS)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Fu),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=nk(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let h;const m=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=ek(m,250),cs.hasAnimatedSinceResize&&(cs.hasAnimatedSinceResize=!1,this.nodes.forEach(Kf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:m,hasRelativeTargetChanged:y,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||c.getDefaultTransition()||xk,{onLayoutAnimationStart:P,onLayoutAnimationComplete:g}=c.getProps(),d=!this.targetLayout||!Jm(this.targetLayout,x)||y,f=!m&&y;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||f||m&&(d||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,f);const v={...Lu(w,"layout"),onPlay:P,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v)}else m||Kf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Dt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(mk),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Gf);return}this.isUpdating||this.nodes.forEach(fk),this.isUpdating=!1,this.nodes.forEach(dk),this.nodes.forEach(ok),this.nodes.forEach(ak),this.clearAllSnapshots();const a=performance.now();xe.delta=sn(0,1e3/60,a-xe.timestamp),xe.timestamp=a,xe.isProcessing=!0,Go.update.process(xe),Go.preRender.process(xe),Go.render.process(xe),xe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ck),this.sharedNodes.forEach(gk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,W.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){W.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ie(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!qm(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||pn(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),wk(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return ie();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&($t(a.x,l.offset.x),$t(a.y,l.offset.y)),a}removeElementScroll(o){const a=ie();He(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:h}=u;if(u!==this.root&&c&&h.layoutScroll){if(c.isRoot){He(a,o);const{scroll:m}=this.root;m&&($t(a.x,-m.offset.x),$t(a.y,-m.offset.y))}$t(a.x,c.offset.x),$t(a.y,c.offset.y)}}return a}applyTransform(o,a=!1){const l=ie();He(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Yn(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),pn(c.latestValues)&&Yn(l,c.latestValues)}return pn(this.latestValues)&&Yn(l,this.latestValues),l}removeTransform(o){const a=ie();He(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!pn(u.latestValues))continue;ul(u.latestValues)&&u.updateSnapshot();const c=ie(),h=u.measurePageBox();He(c,h),bf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return pn(this.latestValues)&&bf(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==xe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:m}=this.options;if(!(!this.layout||!(h||m))){if(this.resolvedRelativeTargetAt=xe.timestamp,!this.targetDelta&&!this.relativeTarget){const y=this.getClosestProjectingParent();y&&y.layout&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),Kr(this.relativeTargetOrigin,this.layout.layoutBox,y.layout.layoutBox),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ie(),this.targetWithTransforms=ie()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),SS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):He(this.target,this.layout.layoutBox),Gm(this.target,this.targetDelta)):He(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const y=this.getClosestProjectingParent();y&&!!y.resumingFrom==!!this.resumingFrom&&!y.options.layoutScroll&&y.target&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),Kr(this.relativeTargetOrigin,this.target,y.target),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}mn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ul(this.parent.latestValues)||Wm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===xe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||h))return;He(this.layoutCorrected,this.layout.layoutBox);const m=this.treeScale.x,y=this.treeScale.y;LS(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:x}=a;if(!x){this.projectionTransform&&(this.projectionDelta=Qn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Qn(),this.projectionDeltaWithTransform=Qn());const w=this.projectionTransform;Gr(this.projectionDelta,this.layoutCorrected,x,this.latestValues),this.projectionTransform=Uf(this.projectionDelta,this.treeScale),(this.projectionTransform!==w||this.treeScale.x!==m||this.treeScale.y!==y)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),mn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=Qn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const m=ie(),y=l?l.source:void 0,x=this.layout?this.layout.source:void 0,w=y!==x,P=this.getStack(),g=!P||P.members.length<=1,d=!!(w&&!g&&this.options.crossfade===!0&&!this.path.some(vk));this.animationProgress=0;let f;this.mixTargetDelta=v=>{const S=v/1e3;Qf(h.x,o.x,S),Qf(h.y,o.y,S),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Kr(m,this.layout.layoutBox,this.relativeParent.layout.layoutBox),yk(this.relativeTarget,this.relativeTargetOrigin,m,S),f&&XS(this.relativeTarget,f)&&(this.isProjectionDirty=!1),f||(f=ie()),He(f,this.relativeTarget)),w&&(this.animationValues=c,HS(c,u,this.latestValues,S,d,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Dt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=W.update(()=>{cs.hasAnimatedSinceResize=!0,this.currentAnimation=rk(0,Wf,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Wf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&tg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ie();const h=Ie(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+h;const m=Ie(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+m}He(a,l),Yn(a,c),Gr(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new ZS),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<Hf.length;c++){const h="rotate"+Hf[c];l[h]&&(u[h]=l[h],o.setStaticValue(h,0))}o.render();for(const c in u)o.setStaticValue(c,u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ik;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=us(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=us(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!pn(this.latestValues)&&(w.transform=c?c({},""):"none",this.hasProjected=!1),w}const m=h.animationValues||h.latestValues;this.applyTransformsToTarget(),u.transform=Uf(this.projectionDeltaWithTransform,this.treeScale,m),c&&(u.transform=c(m,u.transform));const{x:y,y:x}=this.projectionDelta;u.transformOrigin=`${y.origin*100}% ${x.origin*100}% 0`,h.animationValues?u.opacity=h===this?(l=(a=m.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:m.opacityExit:u.opacity=h===this?m.opacity!==void 0?m.opacity:"":m.opacityExit!==void 0?m.opacityExit:0;for(const w in Os){if(m[w]===void 0)continue;const{correct:P,applyTo:g}=Os[w],d=u.transform==="none"?m[w]:P(m[w],h);if(g){const f=g.length;for(let v=0;v<f;v++)u[g[v]]=d}else u[w]=d}return this.options.layoutId&&(u.pointerEvents=h===this?us(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Gf),this.root.sharedNodes.clear()}}}function ok(e){e.updateLayout()}function ak(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?We(h=>{const m=o?n.measuredBox[h]:n.layoutBox[h],y=Ie(m);m.min=r[h].min,m.max=m.min+y}):tg(s,n.layoutBox,r)&&We(h=>{const m=o?n.measuredBox[h]:n.layoutBox[h],y=Ie(r[h]);m.max=m.min+y,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+y)});const a=Qn();Gr(a,r,n.layoutBox);const l=Qn();o?Gr(l,e.applyTransform(i,!0),n.measuredBox):Gr(l,r,n.layoutBox);const u=!qm(a);let c=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:m,layout:y}=h;if(m&&y){const x=ie();Kr(x,n.layoutBox,m.layoutBox);const w=ie();Kr(w,r,y.layoutBox),Jm(x,w)||(c=!0),h.options.layoutRoot&&(e.relativeTarget=w,e.relativeTargetOrigin=x,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function lk(e){mn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function uk(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ck(e){e.clearSnapshot()}function Gf(e){e.clearMeasurements()}function fk(e){e.isLayoutDirty=!1}function dk(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Kf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function hk(e){e.resolveTargetDelta()}function pk(e){e.calcProjection()}function mk(e){e.resetRotation()}function gk(e){e.removeLeadSnapshot()}function Qf(e,t,n){e.translate=X(t.translate,0,n),e.scale=X(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Yf(e,t,n,r){e.min=X(t.min,n.min,r),e.max=X(t.max,n.max,r)}function yk(e,t,n,r){Yf(e.x,t.x,n.x,r),Yf(e.y,t.y,n.y,r)}function vk(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const xk={duration:.45,ease:[.4,0,.1,1]},Xf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Zf=Xf("applewebkit/")&&!Xf("chrome/")?Math.round:re;function qf(e){e.min=Zf(e.min),e.max=Zf(e.max)}function wk(e){qf(e.x),qf(e.y)}function tg(e,t,n){return e==="position"||e==="preserve-aspect"&&!al($f(t),$f(n),.2)}const Sk=eg({attachResizeListener:(e,t)=>Ct(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),na={current:void 0},ng=eg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!na.current){const e=new Sk({});e.mount(window),e.setOptions({layoutScroll:!0}),na.current=e}return na.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),kk={pan:{Feature:IS},drag:{Feature:OS,ProjectionNode:ng,MeasureLayout:Ym}},Ck=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Pk(e){const t=Ck.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function fl(e,t,n=1){const[r,i]=Pk(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return Im(o)?parseFloat(o):o}else return el(i)?fl(i,t,n+1):i}function Ek(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const s=i.get();if(!el(s))return;const o=fl(s,r);o&&i.set(o)});for(const i in t){const s=t[i];if(!el(s))continue;const o=fl(s,r);o&&(t[i]=o,n||(n={}),n[i]===void 0&&(n[i]=s))}return{target:t,transitionEnd:n}}const Tk=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),rg=e=>Tk.has(e),jk=e=>Object.keys(e).some(rg),Jf=e=>e===Rn||e===F,ed=(e,t)=>parseFloat(e.split(", ")[t]),td=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return ed(i[1],t);{const s=r.match(/^matrix\((.+)\)$/);return s?ed(s[1],e):0}},Nk=new Set(["x","y","z"]),Ak=Ci.filter(e=>!Nk.has(e));function Lk(e){const t=[];return Ak.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const cr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:td(4,13),y:td(5,14)};cr.translateX=cr.x;cr.translateY=cr.y;const Rk=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,s=getComputedStyle(i),{display:o}=s,a={};o==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=cr[u](r,s)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=cr[u](l,s)}),e},Mk=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(rg);let s=[],o=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],h=Tr(c);const m=t[l];let y;if(zs(m)){const x=m.length,w=m[0]===null?1:0;c=m[w],h=Tr(c);for(let P=w;P<x&&m[P]!==null;P++)y?Eu(Tr(m[P])===y):y=Tr(m[P])}else y=Tr(m);if(h!==y)if(Jf(h)&&Jf(y)){const x=u.get();typeof x=="string"&&u.set(parseFloat(x)),typeof m=="string"?t[l]=parseFloat(m):Array.isArray(m)&&y===F&&(t[l]=m.map(parseFloat))}else h!=null&&h.transform&&(y!=null&&y.transform)&&(c===0||m===0)?c===0?u.set(y.transform(c)):t[l]=h.transform(m):(o||(s=Lk(e),o=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(m))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=Rk(t,e,a);return s.length&&s.forEach(([c,h])=>{e.getValue(c).set(h)}),e.render(),co&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function Dk(e,t,n,r){return jk(t)?Mk(e,t,n,r):{target:t,transitionEnd:r}}const Fk=(e,t,n,r)=>{const i=Ek(e,t,r);return t=i.target,r=i.transitionEnd,Dk(e,t,n,r)},dl={current:null},ig={current:!1};function _k(){if(ig.current=!0,!!co)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>dl.current=e.matches;e.addListener(t),t()}else dl.current=!1}function Vk(e,t,n){const{willChange:r}=t;for(const i in t){const s=t[i],o=n[i];if(De(s))e.addValue(i,s),Us(r)&&r.add(i);else if(De(o))e.addValue(i,ur(s,{owner:e})),Us(r)&&r.remove(i);else if(o!==s)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(s)}else{const a=e.getStaticValue(i);e.addValue(i,ur(a!==void 0?a:s,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const nd=new WeakMap,sg=Object.keys(hi),Ok=sg.length,rd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Ik=yu.length;class zk{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>W.render(this.render,!1,!0);const{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=ho(n),this.isVariantNode=Up(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const h in c){const m=c[h];a[h]!==void 0&&De(m)&&(m.set(a[h],!1),Us(u)&&u.add(h))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,nd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),ig.current||_k(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:dl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){nd.delete(this.current),this.projection&&this.projection.unmount(),Dt(this.notifyUpdate),Dt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=Ln.has(t),i=n.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&W.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,s){let o,a;for(let l=0;l<Ok;l++){const u=sg[l],{isEnabled:c,Feature:h,ProjectionNode:m,MeasureLayout:y}=hi[u];m&&(o=m),c(n)&&(!this.features[u]&&h&&(this.features[u]=new h(this)),y&&(a=y))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:h,layoutScroll:m,layoutRoot:y}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||h&&Gn(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:m,layoutRoot:y})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ie()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<rd.length;r++){const i=rd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=Vk(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<Ik;r++){const i=yu[r],s=this.props[i];(di(s)||s===!1)&&(n[i]=s)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ur(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Pu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!De(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Fu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class og extends zk{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},s){let o=nS(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),o&&(o=i(o))),s){eS(this,r,o);const a=Fk(this,r,o,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function bk(e){return window.getComputedStyle(e)}class Bk extends og{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(Ln.has(n)){const r=Au(n);return r&&r.default||0}else{const r=bk(t),i=(Qp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Km(t,n)}build(t,n,r,i){xu(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Cu(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;De(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){em(t,n,r,i)}}class $k extends og{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Ln.has(n)){const r=Au(n);return r&&r.default||0}return n=tm.has(n)?n:mu(n),t.getAttribute(n)}measureInstanceViewportBox(){return ie()}scrapeMotionValuesFromProps(t,n){return rm(t,n)}build(t,n,r,i){Su(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){nm(t,n,r,i)}mount(t){this.isSVGTag=ku(t.tagName),super.mount(t)}}const Uk=(e,t)=>vu(e)?new $k(t,{enableHardwareAcceleration:!1}):new Bk(t,{enableHardwareAcceleration:!0}),Hk={layout:{ProjectionNode:ng,MeasureLayout:Ym}},Wk={...yS,...O1,...kk,...Hk},Nr=Wx((e,t)=>P1(e,t,Wk,Uk));var fs={},ag;Object.defineProperty(fs,"__esModule",{value:!0});var Fr=p,ct=C,tt=function(){return tt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},tt.apply(this,arguments)};function Gk(e,t){var n,r;switch(t.type){case"TYPE":return tt(tt({},e),{speed:t.speed,text:(n=t.payload)===null||n===void 0?void 0:n.substring(0,e.text.length+1)});case"DELAY":return tt(tt({},e),{speed:t.payload});case"DELETE":return tt(tt({},e),{speed:t.speed,text:(r=t.payload)===null||r===void 0?void 0:r.substring(0,e.text.length-1)});case"COUNT":return tt(tt({},e),{count:e.count+1});default:return e}}var id=function(e){var t=e.words,n=t===void 0?["Hello World!","This is","a simple Typewriter"]:t,r=e.loop,i=r===void 0?1:r,s=e.typeSpeed,o=s===void 0?80:s,a=e.deleteSpeed,l=a===void 0?50:a,u=e.delaySpeed,c=u===void 0?1500:u,h=e.onLoopDone,m=e.onType,y=e.onDelete,x=e.onDelay,w=ct.useReducer(Gk,{speed:o,text:"",count:0}),P=w[0],g=P.speed,d=P.text,f=P.count,v=w[1],S=ct.useRef(0),k=ct.useRef(!1),E=ct.useRef(!1),T=ct.useRef(!1),j=ct.useRef(!1),L=ct.useCallback(function(){var V=f%n.length,b=n[V];E.current?(v({type:"DELETE",payload:b,speed:l}),d===""&&(E.current=!1,v({type:"COUNT"}))):(v({type:"TYPE",payload:b,speed:o}),T.current=!0,d===b&&(v({type:"DELAY",payload:c}),T.current=!1,j.current=!0,setTimeout(function(){j.current=!1,E.current=!0},c),i>0&&(S.current+=1,S.current/n.length===i&&(j.current=!1,k.current=!0)))),T.current&&m&&m(S.current),E.current&&y&&y(),j.current&&x&&x()},[f,c,l,i,o,n,d,m,y,x]);return ct.useEffect(function(){var V=setTimeout(L,g);return k.current&&clearTimeout(V),function(){return clearTimeout(V)}},[L,g]),ct.useEffect(function(){h&&k.current&&h()},[h]),[d,{isType:T.current,isDelay:j.current,isDelete:E.current,isDone:k.current}]},Kk="styles-module_blinkingCursor__yugAC",Qk="styles-module_blinking__9VXRT";(function(e,t){t===void 0&&(t={});var n=t.insertAt;if(typeof document<"u"){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css",n==="top"&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}})(".styles-module_blinkingCursor__yugAC{color:inherit;font:inherit;left:3px;line-height:inherit;opacity:1;position:relative;top:0}.styles-module_blinking__9VXRT{animation-duration:.8s;animation-iteration-count:infinite;animation-name:styles-module_blink__rqfaf}@keyframes styles-module_blink__rqfaf{0%{opacity:1}to{opacity:0}}");var sd=ct.memo(function(e){var t=e.cursorBlinking,n=t===void 0||t,r=e.cursorStyle,i=r===void 0?"|":r,s=e.cursorColor,o=s===void 0?"inherit":s;return Fr.jsx("span",tt({style:{color:o},className:"".concat(Kk," ").concat(n?Qk:"")},{children:i}))});fs.Cursor=sd,fs.Typewriter=function(e){var t=e.words,n=t===void 0?["Hello World!","This is","a simple Typewriter"]:t,r=e.loop,i=r===void 0?1:r,s=e.typeSpeed,o=s===void 0?80:s,a=e.deleteSpeed,l=a===void 0?50:a,u=e.delaySpeed,c=u===void 0?1500:u,h=e.cursor,m=h!==void 0&&h,y=e.cursorStyle,x=y===void 0?"|":y,w=e.cursorColor,P=w===void 0?"inherit":w,g=e.cursorBlinking,d=g===void 0||g,f=e.onLoopDone,v=e.onType,S=e.onDelay,k=e.onDelete,E=id({words:n,loop:i,typeSpeed:o,deleteSpeed:l,delaySpeed:c,onLoopDone:f,onType:v,onDelay:S,onDelete:k})[0];return Fr.jsxs(Fr.Fragment,{children:[Fr.jsx("span",{children:E}),m&&Fr.jsx(sd,{cursorStyle:x,cursorColor:P,cursorBlinking:d})]})},ag=fs.useTypewriter=id;var lg={exports:{}};(function(e){var t=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var n=function(r){var i=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,s=0,o={},a={manual:r.Prism&&r.Prism.manual,disableWorkerMessageHandler:r.Prism&&r.Prism.disableWorkerMessageHandler,util:{encode:function d(f){return f instanceof l?new l(f.type,d(f.content),f.alias):Array.isArray(f)?f.map(d):f.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(d){return Object.prototype.toString.call(d).slice(8,-1)},objId:function(d){return d.__id||Object.defineProperty(d,"__id",{value:++s}),d.__id},clone:function d(f,v){v=v||{};var S,k;switch(a.util.type(f)){case"Object":if(k=a.util.objId(f),v[k])return v[k];S={},v[k]=S;for(var E in f)f.hasOwnProperty(E)&&(S[E]=d(f[E],v));return S;case"Array":return k=a.util.objId(f),v[k]?v[k]:(S=[],v[k]=S,f.forEach(function(T,j){S[j]=d(T,v)}),S);default:return f}},getLanguage:function(d){for(;d;){var f=i.exec(d.className);if(f)return f[1].toLowerCase();d=d.parentElement}return"none"},setLanguage:function(d,f){d.className=d.className.replace(RegExp(i,"gi"),""),d.classList.add("language-"+f)},currentScript:function(){if(typeof document>"u")return null;if(document.currentScript&&document.currentScript.tagName==="SCRIPT")return document.currentScript;try{throw new Error}catch(S){var d=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(S.stack)||[])[1];if(d){var f=document.getElementsByTagName("script");for(var v in f)if(f[v].src==d)return f[v]}return null}},isActive:function(d,f,v){for(var S="no-"+f;d;){var k=d.classList;if(k.contains(f))return!0;if(k.contains(S))return!1;d=d.parentElement}return!!v}},languages:{plain:o,plaintext:o,text:o,txt:o,extend:function(d,f){var v=a.util.clone(a.languages[d]);for(var S in f)v[S]=f[S];return v},insertBefore:function(d,f,v,S){S=S||a.languages;var k=S[d],E={};for(var T in k)if(k.hasOwnProperty(T)){if(T==f)for(var j in v)v.hasOwnProperty(j)&&(E[j]=v[j]);v.hasOwnProperty(T)||(E[T]=k[T])}var L=S[d];return S[d]=E,a.languages.DFS(a.languages,function(V,b){b===L&&V!=d&&(this[V]=E)}),E},DFS:function d(f,v,S,k){k=k||{};var E=a.util.objId;for(var T in f)if(f.hasOwnProperty(T)){v.call(f,T,f[T],S||T);var j=f[T],L=a.util.type(j);L==="Object"&&!k[E(j)]?(k[E(j)]=!0,d(j,v,null,k)):L==="Array"&&!k[E(j)]&&(k[E(j)]=!0,d(j,v,T,k))}}},plugins:{},highlightAll:function(d,f){a.highlightAllUnder(document,d,f)},highlightAllUnder:function(d,f,v){var S={callback:v,container:d,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",S),S.elements=Array.prototype.slice.apply(S.container.querySelectorAll(S.selector)),a.hooks.run("before-all-elements-highlight",S);for(var k=0,E;E=S.elements[k++];)a.highlightElement(E,f===!0,S.callback)},highlightElement:function(d,f,v){var S=a.util.getLanguage(d),k=a.languages[S];a.util.setLanguage(d,S);var E=d.parentElement;E&&E.nodeName.toLowerCase()==="pre"&&a.util.setLanguage(E,S);var T=d.textContent,j={element:d,language:S,grammar:k,code:T};function L(b){j.highlightedCode=b,a.hooks.run("before-insert",j),j.element.innerHTML=j.highlightedCode,a.hooks.run("after-highlight",j),a.hooks.run("complete",j),v&&v.call(j.element)}if(a.hooks.run("before-sanity-check",j),E=j.element.parentElement,E&&E.nodeName.toLowerCase()==="pre"&&!E.hasAttribute("tabindex")&&E.setAttribute("tabindex","0"),!j.code){a.hooks.run("complete",j),v&&v.call(j.element);return}if(a.hooks.run("before-highlight",j),!j.grammar){L(a.util.encode(j.code));return}if(f&&r.Worker){var V=new Worker(a.filename);V.onmessage=function(b){L(b.data)},V.postMessage(JSON.stringify({language:j.language,code:j.code,immediateClose:!0}))}else L(a.highlight(j.code,j.grammar,j.language))},highlight:function(d,f,v){var S={code:d,grammar:f,language:v};if(a.hooks.run("before-tokenize",S),!S.grammar)throw new Error('The language "'+S.language+'" has no grammar.');return S.tokens=a.tokenize(S.code,S.grammar),a.hooks.run("after-tokenize",S),l.stringify(a.util.encode(S.tokens),S.language)},tokenize:function(d,f){var v=f.rest;if(v){for(var S in v)f[S]=v[S];delete f.rest}var k=new h;return m(k,k.head,d),c(d,k,f,k.head,0),x(k)},hooks:{all:{},add:function(d,f){var v=a.hooks.all;v[d]=v[d]||[],v[d].push(f)},run:function(d,f){var v=a.hooks.all[d];if(!(!v||!v.length))for(var S=0,k;k=v[S++];)k(f)}},Token:l};r.Prism=a;function l(d,f,v,S){this.type=d,this.content=f,this.alias=v,this.length=(S||"").length|0}l.stringify=function d(f,v){if(typeof f=="string")return f;if(Array.isArray(f)){var S="";return f.forEach(function(L){S+=d(L,v)}),S}var k={type:f.type,content:d(f.content,v),tag:"span",classes:["token",f.type],attributes:{},language:v},E=f.alias;E&&(Array.isArray(E)?Array.prototype.push.apply(k.classes,E):k.classes.push(E)),a.hooks.run("wrap",k);var T="";for(var j in k.attributes)T+=" "+j+'="'+(k.attributes[j]||"").replace(/"/g,"&quot;")+'"';return"<"+k.tag+' class="'+k.classes.join(" ")+'"'+T+">"+k.content+"</"+k.tag+">"};function u(d,f,v,S){d.lastIndex=f;var k=d.exec(v);if(k&&S&&k[1]){var E=k[1].length;k.index+=E,k[0]=k[0].slice(E)}return k}function c(d,f,v,S,k,E){for(var T in v)if(!(!v.hasOwnProperty(T)||!v[T])){var j=v[T];j=Array.isArray(j)?j:[j];for(var L=0;L<j.length;++L){if(E&&E.cause==T+","+L)return;var V=j[L],b=V.inside,ue=!!V.lookbehind,te=!!V.greedy,lt=V.alias;if(te&&!V.pattern.global){var Q=V.pattern.toString().match(/[imsuy]*$/)[0];V.pattern=RegExp(V.pattern.source,Q+"g")}for(var ke=V.pattern||V,A=S.next,M=k;A!==f.tail&&!(E&&M>=E.reach);M+=A.value.length,A=A.next){var _=A.value;if(f.length>d.length)return;if(!(_ instanceof l)){var D=1,I;if(te){if(I=u(ke,M,d,ue),!I||I.index>=d.length)break;var ce=I.index,vt=I.index+I[0].length,oe=M;for(oe+=A.value.length;ce>=oe;)A=A.next,oe+=A.value.length;if(oe-=A.value.length,M=oe,A.value instanceof l)continue;for(var Be=A;Be!==f.tail&&(oe<vt||typeof Be.value=="string");Be=Be.next)D++,oe+=Be.value.length;D--,_=d.slice(M,oe),I.index-=M}else if(I=u(ke,0,_,ue),!I)continue;var ce=I.index,$e=I[0],gr=_.slice(0,ce),Mn=_.slice(ce+$e.length),Ot=M+_.length;E&&Ot>E.reach&&(E.reach=Ot);var Ue=A.prev;gr&&(Ue=m(f,Ue,gr),M+=gr.length),y(f,Ue,D);var _u=new l(T,b?a.tokenize($e,b):$e,lt,$e);if(A=m(f,Ue,_u),Mn&&m(f,A,Mn),D>1){var yo={cause:T+","+L,reach:Ot};c(d,f,v,A.prev,M,yo),E&&yo.reach>E.reach&&(E.reach=yo.reach)}}}}}}function h(){var d={value:null,prev:null,next:null},f={value:null,prev:d,next:null};d.next=f,this.head=d,this.tail=f,this.length=0}function m(d,f,v){var S=f.next,k={value:v,prev:f,next:S};return f.next=k,S.prev=k,d.length++,k}function y(d,f,v){for(var S=f.next,k=0;k<v&&S!==d.tail;k++)S=S.next;f.next=S,S.prev=f,d.length-=k}function x(d){for(var f=[],v=d.head.next;v!==d.tail;)f.push(v.value),v=v.next;return f}if(!r.document)return r.addEventListener&&(a.disableWorkerMessageHandler||r.addEventListener("message",function(d){var f=JSON.parse(d.data),v=f.language,S=f.code,k=f.immediateClose;r.postMessage(a.highlight(S,a.languages[v],v)),k&&r.close()},!1)),a;var w=a.util.currentScript();w&&(a.filename=w.src,w.hasAttribute("data-manual")&&(a.manual=!0));function P(){a.manual||a.highlightAll()}if(!a.manual){var g=document.readyState;g==="loading"||g==="interactive"&&w&&w.defer?document.addEventListener("DOMContentLoaded",P):window.requestAnimationFrame?window.requestAnimationFrame(P):window.setTimeout(P,16)}return a}(t);e.exports&&(e.exports=n),typeof Vu<"u"&&(Vu.Prism=n),n.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},n.languages.markup.tag.inside["attr-value"].inside.entity=n.languages.markup.entity,n.languages.markup.doctype.inside["internal-subset"].inside=n.languages.markup,n.hooks.add("wrap",function(r){r.type==="entity"&&(r.attributes.title=r.content.replace(/&amp;/,"&"))}),Object.defineProperty(n.languages.markup.tag,"addInlined",{value:function(i,s){var o={};o["language-"+s]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:n.languages[s]},o.cdata=/^<!\[CDATA\[|\]\]>$/i;var a={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:o}};a["language-"+s]={pattern:/[\s\S]+/,inside:n.languages[s]};var l={};l[i]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return i}),"i"),lookbehind:!0,greedy:!0,inside:a},n.languages.insertBefore("markup","cdata",l)}}),Object.defineProperty(n.languages.markup.tag,"addAttribute",{value:function(r,i){n.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+r+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[i,"language-"+i],inside:n.languages[i]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),n.languages.html=n.languages.markup,n.languages.mathml=n.languages.markup,n.languages.svg=n.languages.markup,n.languages.xml=n.languages.extend("markup",{}),n.languages.ssml=n.languages.xml,n.languages.atom=n.languages.xml,n.languages.rss=n.languages.xml,function(r){var i=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;r.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+i.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+i.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+i.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+i.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:i,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},r.languages.css.atrule.inside.rest=r.languages.css;var s=r.languages.markup;s&&(s.tag.addInlined("style","css"),s.tag.addAttribute("style","css"))}(n),n.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},n.languages.javascript=n.languages.extend("clike",{"class-name":[n.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),n.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,n.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:n.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:n.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:n.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:n.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:n.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),n.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:n.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),n.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),n.languages.markup&&(n.languages.markup.tag.addInlined("script","javascript"),n.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),n.languages.js=n.languages.javascript,function(){if(typeof n>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var r="Loading…",i=function(w,P){return"✖ Error "+w+" while fetching file: "+P},s="✖ Error: File does not exist or is empty",o={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},a="data-src-status",l="loading",u="loaded",c="failed",h="pre[data-src]:not(["+a+'="'+u+'"]):not(['+a+'="'+l+'"])';function m(w,P,g){var d=new XMLHttpRequest;d.open("GET",w,!0),d.onreadystatechange=function(){d.readyState==4&&(d.status<400&&d.responseText?P(d.responseText):d.status>=400?g(i(d.status,d.statusText)):g(s))},d.send(null)}function y(w){var P=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(w||"");if(P){var g=Number(P[1]),d=P[2],f=P[3];return d?f?[g,Number(f)]:[g,void 0]:[g,g]}}n.hooks.add("before-highlightall",function(w){w.selector+=", "+h}),n.hooks.add("before-sanity-check",function(w){var P=w.element;if(P.matches(h)){w.code="",P.setAttribute(a,l);var g=P.appendChild(document.createElement("CODE"));g.textContent=r;var d=P.getAttribute("data-src"),f=w.language;if(f==="none"){var v=(/\.(\w+)$/.exec(d)||[,"none"])[1];f=o[v]||v}n.util.setLanguage(g,f),n.util.setLanguage(P,f);var S=n.plugins.autoloader;S&&S.loadLanguages(f),m(d,function(k){P.setAttribute(a,u);var E=y(P.getAttribute("data-range"));if(E){var T=k.split(/\r\n?|\n/g),j=E[0],L=E[1]==null?T.length:E[1];j<0&&(j+=T.length),j=Math.max(0,Math.min(j-1,T.length)),L<0&&(L+=T.length),L=Math.max(0,Math.min(L,T.length)),k=T.slice(j,L).join(`
`),P.hasAttribute("data-start")||P.setAttribute("data-start",String(j+1))}g.textContent=k,n.highlightElement(g)},function(k){P.setAttribute(a,c),g.textContent=k})}}),n.plugins.fileHighlight={highlight:function(P){for(var g=(P||document).querySelectorAll(h),d=0,f;f=g[d++];)n.highlightElement(f)}};var x=!1;n.fileHighlight=function(){x||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),x=!0),n.plugins.fileHighlight.highlight.apply(this,arguments)}}()})(lg);var Yk=lg.exports;const Xk=od(Yk);Prism.languages.c=Prism.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/});Prism.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}});Prism.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:Prism.languages.c}}}});Prism.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/});delete Prism.languages.c.boolean;const Zk=()=>{const e=C.useRef(null),[t]=ag({words:[`#include <stdio.h>

int main() {
    printf("Hello, welcome to Qochi\\n");
    printf("C Programming Services!\\n");

    // Learn fundamentals to advanced concepts
    for (int i = 1; i <= 5; i++) {
        printf("Level %d: Mastering C\\n", i);
    }

    return 0;
}`],typeSpeed:20,deleteSpeed:50,loop:1});return C.useEffect(()=>{e.current&&Xk.highlightElement(e.current)},[t]),p.jsx("div",{className:"bg-secondary rounded-custom p-6 shadow-custom border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 max-w-full overflow-hidden",children:p.jsx("pre",{className:"code-block relative whitespace-pre-wrap break-words",children:p.jsx("code",{ref:e,className:"language-c text-sm md:text-base",children:t})})})},qk="/assets/Aleksandre-BD3wKPBE.jpg",Jk=()=>{const e=Np(),t=()=>{e("/contact"),window.scrollTo({top:0,behavior:"smooth"})};return p.jsxs("div",{className:"space-y-20",children:[p.jsx("section",{className:"py-16 md:py-24",children:p.jsx("div",{className:"container mx-auto px-4",children:p.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[p.jsxs("div",{className:"md:w-1/2 mb-10 md:mb-0",children:[p.jsxs(Nr.h1,{className:"text-4xl md:text-5xl font-bold mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:["Master"," ",p.jsx("span",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 bg-clip-text text-transparent bg-[length:200%_200%] animate-gradient",children:"C Programming"})," ","with Expert Guidance"]}),p.jsxs(Nr.p,{className:"text-xl text-gray-300 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:["Personalized tutoring sessions to help you understand the fundamentals ",p.jsx("br",{})," and advanced concepts of C programming."]}),p.jsxs(Nr.div,{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:[p.jsxs("button",{onClick:t,className:"btn-primary flex items-center justify-center",children:["Book a Session",p.jsx(px,{className:"ml-2 h-5 w-5"})]}),p.jsx(it,{to:"/contact",className:"btn-secondary flex items-center justify-center",children:"Learn More"})]})]}),p.jsx(Nr.div,{className:"md:w-1/2",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:p.jsx(Zk,{})})]})})}),p.jsx("section",{className:"py-16 bg-gradient-custom rounded-xl",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsx("h2",{className:"section-title text-center",children:"What is C Used For and Why Learn It?"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12",children:[p.jsxs("div",{className:"card",children:[p.jsx(Ax,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"System Programming"}),p.jsx("p",{className:"text-gray-300",children:"C is the foundation of operating systems like Linux and Windows. Learn to write efficient system-level code."})]}),p.jsxs("div",{className:"card",children:[p.jsx(Tx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Embedded Systems"}),p.jsx("p",{className:"text-gray-300",children:"Power IoT devices, microcontrollers, and real-time systems with C programming skills."})]}),p.jsxs("div",{className:"card",children:[p.jsx(wx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Performance Critical Apps"}),p.jsx("p",{className:"text-gray-300",children:"Build high-performance applications where speed and efficiency matter most."})]}),p.jsxs("div",{className:"card",children:[p.jsx(jx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Security Software"}),p.jsx("p",{className:"text-gray-300",children:"Develop secure applications and understand low-level security concepts."})]}),p.jsxs("div",{className:"card",children:[p.jsx(Op,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Foundation for Other Languages"}),p.jsx("p",{className:"text-gray-300",children:"Master C to better understand modern programming languages built upon its concepts."})]}),p.jsxs("div",{className:"card",children:[p.jsx(mx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Career Opportunities"}),p.jsx("p",{className:"text-gray-300",children:"Open doors to careers in systems programming, embedded development, and more."})]})]})]})}),p.jsx("section",{className:"py-2 bg-gradient-custom rounded-xl",children:p.jsx("div",{className:"container mx-auto px-1",children:p.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-10",children:[p.jsxs("div",{className:"md:w-1/2 space-y-6",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white",children:"Meet Your C Programming Tutor"}),p.jsx("p",{className:"text-xl text-gray-300",children:"With extensive experience in C programming and a passion for teaching, I'm here to help you master the fundamentals and advanced concepts of C programming."}),p.jsxs("a",{href:"https://github.com/AppleItsme/",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 bg-accent hover:bg-accent-hover text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105",children:[p.jsx(Ip,{className:"h-5 w-5"}),p.jsx("span",{children:"View My Portfolio"})]})]}),p.jsx("div",{className:"md:w-1/4 pl-10 order-5 md:order-5",children:p.jsx("div",{className:"relative",children:p.jsx(Nr.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"w-full h-[400px] rounded-xl overflow-hidden border-2 border-accent/20 hover:border-accent/40 transition-all duration-300",children:p.jsx("img",{src:qk,alt:"Aleksandre - C Programming Tutor",className:"w-full h-full object-cover object-center"})})})})]})})}),p.jsx("section",{className:"py-16",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsx("h2",{className:"section-title text-center",children:"Why Choose My Tutoring Services"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12",children:[p.jsxs("div",{className:"card",children:[p.jsx(vx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Comprehensive Curriculum"}),p.jsx("p",{className:"text-gray-300",children:"From basic syntax to advanced memory management, my curriculum covers all aspects of C programming."})]}),p.jsxs("div",{className:"card",children:[p.jsx(zp,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Personalized Approach"}),p.jsx("p",{className:"text-gray-300",children:"Tailored lessons based on your learning style, pace, and specific goals in C programming."})]}),p.jsxs("div",{className:"card",children:[p.jsx(Nx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Industry Experience"}),p.jsx("p",{className:"text-gray-300",children:"Learn from a tutor with real-world experience in software development and C programming."})]}),p.jsxs("div",{className:"card",children:[p.jsx(Vp,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Flexible Scheduling"}),p.jsx("p",{className:"text-gray-300",children:"Book sessions at times that work for you, with options for recurring or one-time lessons."})]}),p.jsxs("div",{className:"card",children:[p.jsx(gx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Practical Projects"}),p.jsx("p",{className:"text-gray-300",children:"Reinforce your learning with hands-on projects that build your portfolio and confidence."})]}),p.jsxs("div",{className:"card",children:[p.jsx(Rx,{className:"h-12 w-12 text-accent mb-4"}),p.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Ongoing Support"}),p.jsx("p",{className:"text-gray-300",children:"Get help with questions and code reviews between sessions via email or messaging."})]})]})]})}),p.jsx("section",{className:"py-16 bg-gradient-custom rounded-xl",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsx("h2",{className:"section-title text-center",children:"Topics I Cover"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mt-12",children:[p.jsxs("div",{className:"card",children:[p.jsx("h3",{className:"text-xl font-semibold mb-4 text-accent",children:"Fundamentals"}),p.jsxs("ul",{className:"space-y-3",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"C Syntax and Basic Programming Concepts"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Data Types, Variables, and Operators"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Control Structures (if, switch, loops)"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Functions and Program Structure"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Arrays and Strings"})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsx("h3",{className:"text-xl font-semibold mb-4 text-accent",children:"Intermediate"}),p.jsxs("ul",{className:"space-y-3",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Pointers and Memory Management"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Structures, Unions, and Enumerations"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Standard Library Functions"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Dynamic Memory Allocation"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Command Line Arguments"})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsx("h3",{className:"text-xl font-semibold mb-4 text-accent",children:"Advanced"}),p.jsxs("ul",{className:"space-y-3",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Advanced Pointer Concepts"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Bit Manipulation"})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsx("h3",{className:"text-xl font-semibold mb-4 text-accent",children:"Practical Applications"}),p.jsxs("ul",{className:"space-y-3",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Data Structures Implementation"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Algorithms in C"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Debugging Techniques"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Performance Optimization"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(he,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Building Real-world Applications"})]})]})]})]})]})}),p.jsx("section",{id:"pricing",className:"py-16",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsx("h2",{className:"section-title text-center",children:"Tutoring Packages"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12",children:[p.jsxs("div",{className:"card text-center hover:border-accent hover:border-2 transition-all duration-300",children:[p.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Basic Package"}),p.jsxs("div",{className:"text-4xl font-bold mb-6",children:[p.jsx("span",{className:"text-accent",children:"$45"}),p.jsx("span",{className:"text-xl text-gray-400",children:"/hour"})]}),p.jsxs("ul",{className:"space-y-3 text-left mb-8",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"1-on-1 Personalized Sessions"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Homework Help"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Basic Concept Coverage"})]})]}),p.jsx("button",{onClick:t,className:"btn-primary block w-full",children:"Get Started"})]}),p.jsxs("div",{className:"card text-center transform scale-105 border-2 border-accent",children:[p.jsx("div",{className:"absolute top-0 right-0 bg-accent text-white px-4 py-1 rounded-bl-lg rounded-tr-lg",children:"Popular"}),p.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Standard Package"}),p.jsxs("div",{className:"text-4xl font-bold mb-6",children:[p.jsx("span",{className:"text-accent",children:"$60"}),p.jsx("span",{className:"text-xl text-gray-400",children:"/hour"})]}),p.jsxs("ul",{className:"space-y-3 text-left mb-8",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"All Basic Features"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Project Development"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Code Review Support"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Email Support"})]})]}),p.jsx("button",{onClick:t,className:"btn-primary block w-full",children:"Get Started"})]}),p.jsxs("div",{className:"card text-center hover:border-accent hover:border-2 transition-all duration-300",children:[p.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Premium Package"}),p.jsxs("div",{className:"text-4xl font-bold mb-6",children:[p.jsx("span",{className:"text-accent",children:"$75"}),p.jsx("span",{className:"text-xl text-gray-400",children:"/hour"})]}),p.jsxs("ul",{className:"space-y-3 text-left mb-8",children:[p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"All Standard Features"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Advanced Topics Coverage"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Priority Support"})]}),p.jsxs("li",{className:"flex items-center",children:[p.jsx(Je,{className:"h-5 w-5 text-accent mr-2"}),p.jsx("span",{children:"Interview Preparation"})]})]}),p.jsx("button",{onClick:t,className:"btn-primary block w-full",children:"Get Started"})]})]})]})}),p.jsx("section",{className:"py-16",children:p.jsx("div",{className:"container mx-auto px-4",children:p.jsxs("div",{className:"card text-center py-16",children:[p.jsx("h2",{className:"text-4xl font-bold mb-4",children:"Ready to Master C Programming?"}),p.jsx("p",{className:"text-xl text-gray-300 mb-8",children:"Book your first session today and start your journey to becoming proficient in C programming with personalized guidance."}),p.jsxs("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[p.jsx("button",{onClick:t,className:"btn-primary",children:"Book a Session"}),p.jsx(it,{to:"/contact",className:"btn-secondary",children:"Contact Me"})]})]})})})]})},eC=()=>{const[e,t]=C.useState({name:"",email:"",level:"beginner",description:""}),[n,r]=C.useState(0),i=500;C.useEffect(()=>{const a=document.createElement("script");return a.src="https://assets.calendly.com/assets/external/widget.js",a.async=!0,document.body.appendChild(a),()=>{document.body.removeChild(a)}},[]);const s=a=>{const{name:l,value:u}=a.target;t(c=>({...c,[l]:u})),l==="description"&&r(u.length)},o=a=>{a.preventDefault(),console.log("Form submitted:",e),alert("Thank you for your message! I will get back to you soon."),t({name:"",email:"",level:"beginner",description:""}),r(0)};return p.jsxs("div",{className:"space-y-16",children:[p.jsxs("section",{className:"py-8",children:[p.jsx("h1",{className:"section-title text-center",children:"Get in Touch"}),p.jsx("p",{className:"text-xl text-gray-300 text-center max-w-3xl mx-auto",children:"Have questions about C programming tutoring? Reach out and I'll be happy to help you on your learning journey."})]}),p.jsx("section",{className:"py-8",children:p.jsx("div",{className:"container mx-auto px-4",children:p.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[p.jsxs("div",{className:"card",children:[p.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Send Me a Message"}),p.jsx("form",{onSubmit:o,children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300 mb-1",children:"Your Name"}),p.jsxs("div",{className:"relative",children:[p.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:s,required:!0,className:"input-field pl-10",placeholder:"Enter your full name"}),p.jsx(zp,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"})]})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-1",children:"Email Address"}),p.jsxs("div",{className:"relative",children:[p.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:s,required:!0,className:"input-field pl-10",placeholder:"Enter your email address"}),p.jsx(Ja,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"})]})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"level",className:"block text-sm font-medium text-gray-300 mb-1",children:"Your Level"}),p.jsxs("div",{className:"relative",children:[p.jsxs("select",{id:"level",name:"level",value:e.level,onChange:s,required:!0,className:"input-field pl-10",children:[p.jsx("option",{value:"beginner",children:"Beginner"}),p.jsx("option",{value:"intermediate",children:"Intermediate"}),p.jsx("option",{value:"advanced",children:"Advanced"})]}),p.jsx(yx,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"})]})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description of Your Problem/Requirements"}),p.jsxs("div",{className:"relative",children:[p.jsx("textarea",{id:"description",name:"description",value:e.description,onChange:s,required:!0,rows:5,maxLength:i,className:"input-field pl-10",placeholder:"Please describe what you'd like to learn or any specific problems you're facing..."}),p.jsx(Cx,{className:"absolute left-3 top-4 h-5 w-5 text-gray-400"}),p.jsxs("div",{className:"absolute bottom-2 right-2 text-sm text-gray-400",children:[n,"/",i]})]})]}),p.jsxs("button",{type:"submit",className:"btn-primary w-full flex items-center justify-center",children:[p.jsx(Ex,{className:"mr-2 h-5 w-5"}),"Send Message"]})]})})]}),p.jsxs("div",{className:"space-y-8",children:[p.jsxs("div",{className:"card",children:[p.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Contact Information"}),p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-start",children:[p.jsx(Ja,{className:"h-6 w-6 text-accent mr-4 mt-1"}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-medium",children:"Email"}),p.jsx("p",{className:"text-gray-300",children:"<EMAIL>"})]})]}),p.jsxs("div",{className:"flex items-start",children:[p.jsx(Vp,{className:"h-6 w-6 text-accent mr-4 mt-1"}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-medium",children:"Response Time"}),p.jsx("p",{className:"text-gray-300",children:"Usually within 24 hours"})]})]}),p.jsxs("div",{className:"flex items-start",children:[p.jsx(xx,{className:"h-6 w-6 text-accent mr-4 mt-1"}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-medium",children:"Available Hours"}),p.jsx("p",{className:"text-gray-300",children:"Monday - Friday: 9AM - 6PM EST"})]})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Schedule a Session"}),p.jsx("div",{className:"calendly-inline-widget","data-url":"https://calendly.com/pilybas-edgaras/tutoring-session",style:{minWidth:"320px",height:"700px"}})]})]})]})})})]})},tC=()=>{const{pathname:e}=Vt();return C.useEffect(()=>{window.scrollTo({top:0,behavior:"smooth"})},[e]),null};function nC(){const[e]=C.useState(!0);return C.useEffect(()=>(document.body.style.paddingTop="80px",()=>{document.body.style.paddingTop="0"}),[]),p.jsx("div",{className:`min-h-screen ${e?"bg-background":"bg-gray-100"}`,children:p.jsxs(nx,{children:[p.jsx(tC,{}),p.jsx(Dx,{}),p.jsx("main",{className:"container mx-auto px-4 py-8",children:p.jsxs(D0,{children:[p.jsx(Za,{path:"/",element:p.jsx(Jk,{})}),p.jsx(Za,{path:"/contact",element:p.jsx(eC,{})})]})}),p.jsx(Fx,{})]})})}ra.createRoot(document.getElementById("root")).render(p.jsx(yl.StrictMode,{children:p.jsx(nC,{})}));
