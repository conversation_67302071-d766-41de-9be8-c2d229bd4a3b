{"version": 3, "sources": ["../../react-simple-typewriter/src/reducer.ts", "../../react-simple-typewriter/src/hooks/useTypewriter.tsx", "../../react-simple-typewriter/node_modules/style-inject/dist/style-inject.es.js", "../../react-simple-typewriter/src/components/Cursor.tsx", "../../react-simple-typewriter/src/components/Typewriter.tsx"], "sourcesContent": ["export type State = {\r\n  speed: number\r\n  text: string\r\n  count: number\r\n}\r\n\r\nexport type Action =\r\n  | { type: 'DELAY'; payload: number }\r\n  | { type: 'TYPE'; payload: string; speed: number }\r\n  | { type: 'DELETE'; payload: string; speed: number }\r\n  | { type: 'COUNT' }\r\n\r\nexport function reducer(state: State, action: Action): State {\r\n  switch (action.type) {\r\n    case 'TYPE':\r\n      return {\r\n        ...state,\r\n        speed: action.speed,\r\n        text: action.payload?.substring(0, state.text.length + 1)\r\n      }\r\n    case 'DELAY':\r\n      return {\r\n        ...state,\r\n        speed: action.payload\r\n      }\r\n    case 'DELETE':\r\n      return {\r\n        ...state,\r\n        speed: action.speed,\r\n        text: action.payload?.substring(0, state.text.length - 1)\r\n      }\r\n    case 'COUNT':\r\n      return {\r\n        ...state,\r\n        count: state.count + 1\r\n      }\r\n    default:\r\n      return state\r\n  }\r\n}\r\n", "import { useCallback, useEffect, useRef, useReducer } from 'react'\r\nimport { reducer } from '../reducer'\r\n\r\nexport type TypewriterProps = {\r\n  /** Callback Function that is triggered when loops are completed. available if loop is > `0` */\r\n  onLoopDone?: () => void\r\n  /** Callback Function that is triggered while typing with `typed` words count passed */\r\n  onType?: (count: number) => void\r\n  /** Callback Function that is triggered while deleting */\r\n  onDelete?: () => void\r\n  /** Callback Function that is triggered on typing delay */\r\n  onDelay?: () => void\r\n  /** Array of strings holding the words */\r\n  words: string[]\r\n  /** Control how many times to run. `0 | false` to run infinitely */\r\n  loop?: number | boolean\r\n  /** Character typing speed in Milliseconds */\r\n  typeSpeed?: number\r\n  /** Character deleting speed in Milliseconds */\r\n  deleteSpeed?: number\r\n  /** Delay time between the words in Milliseconds */\r\n  delaySpeed?: number\r\n}\r\n\r\nexport type TypewriterHelper = {\r\n  /** `true` if currently typing */\r\n  isType: boolean\r\n  /** `true` if on delay */\r\n  isDelay: boolean\r\n  /** `true` if currently deleting */\r\n  isDelete: boolean\r\n  /** `true` if all loops are done */\r\n  isDone: boolean\r\n}\r\n\r\nexport const useTypewriter = ({\r\n  words = ['Hello World!', 'This is', 'a simple Typewriter'],\r\n  loop = 1,\r\n  typeSpeed = 80,\r\n  deleteSpeed = 50,\r\n  delaySpeed = 1500,\r\n  onLoopDone,\r\n  onType,\r\n  onDelete,\r\n  onDelay\r\n}: TypewriterProps): [string, TypewriterHelper] => {\r\n  const [{ speed, text, count }, dispatch] = useReducer(reducer, {\r\n    speed: typeSpeed,\r\n    text: '',\r\n    count: 0\r\n  })\r\n\r\n  // Refs\r\n  const loops = useRef(0)\r\n  const isDone = useRef(false)\r\n  const isDelete = useRef(false)\r\n  const isType = useRef(false)\r\n  const isDelay = useRef(false)\r\n\r\n  const handleTyping = useCallback(() => {\r\n    const index = count % words.length\r\n    const fullWord = words[index]\r\n\r\n    if (!isDelete.current) {\r\n      dispatch({ type: 'TYPE', payload: fullWord, speed: typeSpeed })\r\n      isType.current = true\r\n\r\n      if (text === fullWord) {\r\n        dispatch({ type: 'DELAY', payload: delaySpeed })\r\n        isType.current = false\r\n        isDelay.current = true\r\n\r\n        setTimeout(() => {\r\n          isDelay.current = false\r\n          isDelete.current = true\r\n        }, delaySpeed)\r\n\r\n        if (loop > 0) {\r\n          loops.current += 1\r\n          if (loops.current / words.length === loop) {\r\n            isDelay.current = false\r\n            isDone.current = true\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      dispatch({ type: 'DELETE', payload: fullWord, speed: deleteSpeed })\r\n      if (text === '') {\r\n        isDelete.current = false\r\n        dispatch({ type: 'COUNT' })\r\n      }\r\n    }\r\n\r\n    if (isType.current) {\r\n      if (onType) onType(loops.current)\r\n    }\r\n\r\n    if (isDelete.current) {\r\n      if (onDelete) onDelete()\r\n    }\r\n\r\n    if (isDelay.current) {\r\n      if (onDelay) onDelay()\r\n    }\r\n  }, [\r\n    count,\r\n    delaySpeed,\r\n    deleteSpeed,\r\n    loop,\r\n    typeSpeed,\r\n    words,\r\n    text,\r\n    onType,\r\n    onDelete,\r\n    onDelay\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const typing = setTimeout(handleTyping, speed)\r\n\r\n    if (isDone.current) clearTimeout(typing)\r\n\r\n    return () => clearTimeout(typing)\r\n  }, [handleTyping, speed])\r\n\r\n  useEffect(() => {\r\n    if (!onLoopDone) return\r\n\r\n    if (isDone.current) {\r\n      onLoopDone()\r\n    }\r\n  }, [onLoopDone])\r\n\r\n  return [\r\n    text,\r\n    {\r\n      isType: isType.current,\r\n      isDelay: isDelay.current,\r\n      isDelete: isDelete.current,\r\n      isDone: isDone.current\r\n    }\r\n  ]\r\n}\r\n", "function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", "import { ReactNode, memo } from 'react'\r\nimport styles from '../styles.module.css'\r\n\r\nexport type CursorProps = {\r\n  /** Enable cursor blinking animation */\r\n  cursorBlinking?: boolean\r\n  /** Change cursor style */\r\n  cursorStyle?: ReactNode\r\n  /** Change cursor color */\r\n  cursorColor?: string\r\n}\r\n\r\nconst MemoizedCursor = ({\r\n  cursorBlinking = true,\r\n  cursorStyle = '|',\r\n  cursorColor = 'inherit'\r\n}: CursorProps): JSX.Element => {\r\n  return (\r\n    <span\r\n      style={{ color: cursorColor }}\r\n      className={`${styles.blinkingCursor} ${\r\n        cursorBlinking ? styles.blinking : ''\r\n      }`}\r\n    >\r\n      {cursorStyle}\r\n    </span>\r\n  )\r\n}\r\n\r\nexport const Cursor = memo(MemoizedCursor)\r\n", "import { TypewriterProps, useTypewriter } from '../hooks/useTypewriter'\r\nimport { Cursor, CursorProps } from './Cursor'\r\n\r\ntype ComponentProps = {\r\n  /** Show / Hide the cursor */\r\n  cursor?: boolean\r\n} & TypewriterProps &\r\n  CursorProps\r\n\r\n/**\r\n * A Simple React Component for adding a nice Typewriter effect to your project.\r\n */\r\nexport const Typewriter = ({\r\n  words = ['Hello World!', 'This is', 'a simple Typewriter'],\r\n  loop = 1,\r\n  typeSpeed = 80,\r\n  deleteSpeed = 50,\r\n  delaySpeed = 1500,\r\n  cursor = false,\r\n  cursorStyle = '|',\r\n  cursorColor = 'inherit',\r\n  cursorBlinking = true,\r\n  onLoopDone,\r\n  onType,\r\n  onDelay,\r\n  onDelete\r\n}: ComponentProps): JSX.Element => {\r\n  const [text] = useTypewriter({\r\n    words,\r\n    loop,\r\n    typeSpeed,\r\n    deleteSpeed,\r\n    delaySpeed,\r\n    onLoopDone,\r\n    onType,\r\n    onDelay,\r\n    onDelete\r\n  })\r\n\r\n  return (\r\n    <>\r\n      <span>{text}</span>\r\n      {cursor && (\r\n        <Cursor\r\n          cursorStyle={cursorStyle}\r\n          cursorColor={cursorColor}\r\n          cursorBlinking={cursorBlinking}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAYgB,aAAAA,EAAQC,IAAcC,IAAAA;AAAAA,UAAAA,IAAAA;AACpC,cAAQA,GAAOC,MAAAA;QACb,KAAK;AACH,iBACKC,EAAAA,EAAA,CAAA,GAAAH,EAAAA,GACH,EAAAI,OAAOH,GAAOG,OACdC,MAAoB,UAAdC,KAAAL,GAAOM,YAAAA,WAAOD,KAAAA,SAAAA,GAAEE,UAAU,GAAGR,GAAMK,KAAKI,SAAS,CAAA,EAAA,CAAA;QAE3D,KAAK;AACH,iBAAAN,EAAAA,EAAA,CAAA,GACKH,EAAAA,GACH,EAAAI,OAAOH,GAAOM,QAAAA,CAAAA;QAElB,KAAK;AACH,iBACKJ,EAAAA,EAAA,CAAA,GAAAH,EAAAA,GACH,EAAAI,OAAOH,GAAOG,OACdC,MAAoB,UAAdK,KAAAT,GAAOM,YAAAA,WAAOG,KAAAA,SAAAA,GAAEF,UAAU,GAAGR,GAAMK,KAAKI,SAAS,CAAA,EAAA,CAAA;QAE3D,KAAK;AACH,iBACKN,EAAAA,EAAA,CAAA,GAAAH,EAAAA,GAAAA,EACHW,OAAOX,GAAMW,QAAQ,EAAA,CAAA;QAEzB;AACE,iBAAOX;MAAAA;IAEb;ACJO,QAAMY,IAAgB,SAACN,IAAAA;AAAAA,UAC5BI,KAA0DJ,GAAAO,OAA1DA,KAAAA,WAAQH,KAAA,CAAC,gBAAgB,WAAW,qBAAA,IAAsBA,IAC1DI,KAAQR,GAAAS,MAARA,KAAAA,WAAID,KAAG,IAACA,IACRE,KAAcV,GAAAW,WAAdA,IAAAA,WAASD,KAAG,KAAEA,IACdE,IAAgBZ,GAAAa,aAAhBA,IAAAA,WAAcD,IAAA,KAAA,GACdE,IAAAd,GAAAe,YAAAA,IAAAA,WAAaD,IAAA,OAAA,GACbE,IAAUhB,GAAAgB,YACVC,IAAMjB,GAAAiB,QACNC,IAAQlB,GAAAkB,UACRC,IAAOnB,GAAAmB,SAEDC,IAAqCC,EAAUA,WAAC5B,GAAS,EAC7DK,OAAOa,GACPZ,MAAM,IACNM,OAAO,EAAA,CAAA,GAHFiB,IAAAF,EAAA,CAAA,GAAEtB,IAAAA,EAAAA,OAAOC,IAAIuB,EAAAvB,MAAEM,IAAKiB,EAAAjB,OAAIkB,IAAAA,EAAAA,CAAAA,GAOzBC,IAAQC,EAAAA,OAAO,CAAA,GACfC,IAASD,EAAAA,OAAAA,KAAO,GAChBE,IAAWF,EAAAA,OAAAA,KAAO,GAClBG,IAASH,EAAAA,OAAAA,KAAO,GAChBI,IAAUJ,EAAAA,OAAAA,KAAO,GAEjBK,IAAeC,EAAAA,YAAY,WAAA;AAC/B,YAAMC,KAAQ3B,IAAQE,GAAMJ,QACtB8B,KAAW1B,GAAMyB,EAAAA;AAElBL,UAASO,WAuBZX,EAAS,EAAE3B,MAAM,UAAUK,SAASgC,IAAUnC,OAAOe,EAAAA,CAAAA,GACxC,OAATd,MACF4B,EAASO,UAAAA,OACTX,EAAS,EAAE3B,MAAM,QAAA,CAAA,OAzBnB2B,EAAS,EAAE3B,MAAM,QAAQK,SAASgC,IAAUnC,OAAOa,EAAAA,CAAAA,GACnDiB,EAAOM,UAAAA,MAEHnC,MAASkC,OACXV,EAAS,EAAE3B,MAAM,SAASK,SAASc,EAAAA,CAAAA,GACnCa,EAAOM,UAAAA,OACPL,EAAQK,UAAAA,MAERC,WAAW,WAAA;AACTN,YAAQK,UAAAA,OACRP,EAASO,UAAAA;QACV,GAAEnB,CAAAA,GAECN,KAAO,MACTe,EAAMU,WAAW,GACbV,EAAMU,UAAU3B,GAAMJ,WAAWM,OACnCoB,EAAQK,UAAAA,OACRR,EAAOQ,UAAAA,UAYXN,EAAOM,WACLjB,KAAQA,EAAOO,EAAMU,OAAAA,GAGvBP,EAASO,WACPhB,KAAUA,EAAAA,GAGZW,EAAQK,WACNf,KAASA,EAAAA;MAEjB,GAAG,CACDd,GACAU,GACAF,GACAJ,IACAE,GACAJ,IACAR,GACAkB,GACAC,GACAC,CAAAA,CAAAA;AAmBF,aAhBAiB,EAAAA,UAAU,WAAA;AACR,YAAMC,KAASF,WAAWL,GAAchC,CAAAA;AAIxC,eAFI4B,EAAOQ,WAASI,aAAaD,EAAAA,GAE1B,WAAA;AAAM,iBAAAC,aAAaD,EAAAA;QAAO;MACnC,GAAG,CAACP,GAAchC,CAAAA,CAAAA,GAElBsC,EAAAA,UAAU,WAAA;AACHpB,aAEDU,EAAOQ,WACTlB,EAAAA;MAEJ,GAAG,CAACA,CAAAA,CAAAA,GAEG,CACLjB,GACA,EACE6B,QAAQA,EAAOM,SACfL,SAASA,EAAQK,SACjBP,UAAUA,EAASO,SACnBR,QAAQA,EAAOQ,QAAAA,CAAAA;IAGrB;AAAA,QAAA,IAAA;AAAA,QAAA,IAAA;AAAA,KC9IA,SAAqBK,IAAKC,IAAAA;AAAAA,iBACnBA,OAAiBA,KAAM,CAAA;AAC5B,UAAIC,KAAWD,GAAIC;AAEnB,UAAKF,MAA2B,eAAA,OAAbG,UAAnB;AAEA,YAAIC,KAAOD,SAASC,QAAQD,SAASE,qBAAqB,MAAA,EAAQ,CAAA,GAC9DC,KAAQH,SAASI,cAAc,OAAA;AACnCD,QAAAA,GAAMjD,OAAO,YAEI,UAAb6C,MACEE,GAAKI,aACPJ,GAAKK,aAAaH,IAAOF,GAAKI,UAAAA,IAKhCJ,GAAKM,YAAYJ,EAAAA,GAGfA,GAAMK,aACRL,GAAMK,WAAWC,UAAUZ,KAE3BM,GAAMI,YAAYP,SAASU,eAAeb,EAAAA,CAAAA;MAnBY;IAqB1D,EAAA,qUAAA;ACbA,QAiBac,IAASC,EAAIA,KAjBH,SAACtD,IAAAA;AACtB,UAAAI,KAAAJ,GAAAuD,gBAAAA,KAAAA,WAAcnD,MAAOA,IACrBI,KAAAA,GAAAA,aAAAgD,IAAAA,WAAchD,KAAA,MAAAiD,IACd/C,IAAAV,GAAA0D,aAAAA,IAAAA,WAAWhD,IAAG,YAASA;AAEvB,aACEiD,EAAAA,IAAA,QAAA9D,EAAA,EACEgD,OAAO,EAAEe,OAAOF,EAAAA,GAChBG,WAAW,GAAAC,OAAGC,GACZ,GAAA,EAAAD,OAAAP,KAAiBQ,IAAkB,EAAA,EAAA,GACnC,EAAAC,UAEDR,EAAAA,CAAAA,CAAAA;IAGP,CAAA;AAAA,YAAA,SAAA,GAAA,QAAA,aCf0B,SAACxD,IAAAA;AACzB,UAAAI,KAAAJ,GAAAO,OAAAA,KAAAA,WAAKH,KAAG,CAAC,gBAAgB,WAAW,qBAAA,IAAA6D,IACpCzD,KAAAR,GAAAS,MAAAA,KAAAA,WAAAA,KAAO,IAACD,IACRE,IAAAV,GAAAW,WAAAA,IAAAA,WAASD,IAAG,KAAEA,GACdE,IAAgBZ,GAAAa,aAAhBA,IAAAA,WAAWD,IAAG,KAAEA,GAChBE,IAAAA,GAAAA,YAAAC,IAAAA,WAAAA,IAAa,OAAID,GACjBM,IAAApB,GAAAkE,QAAAA,IAAAA,WAAM9C,KAAQA,GACdE,IAAiBtB,GAAAwD,aAAjBA,IAAAA,WAAWlC,IAAG,MAAGA,GACjB6C,IAAuBnE,GAAA0D,aAAvBA,IAAAA,WAAcS,IAAA,YAASA,GACvBC,IAAAA,GAAAA,gBAAAb,IAAAA,WAAiBa,KAAAA,GACjBpD,IAAUhB,GAAAgB,YACVC,IAAMjB,GAAAiB,QACNE,IAAOnB,GAAAmB,SACPD,IAAQlB,GAAAkB,UAEDnB,IAAQO,EAAc,EAC3BC,OAAKA,IACLE,MAAIA,IACJE,WAASA,GACTE,aAAWA,GACXE,YAAUA,GACVC,YAAUA,GACVC,QAAMA,GACNE,SAAOA,GACPD,UAAQA,EAAAA,CAAAA,EAAAA,CAAAA;AAGV,aACEmD,EAAAA,KACEC,EAAAA,UAAA,EAAAN,UAAA,CAAAL,EAAAA,IAAA,QAAA,EAAAK,UAAOjE,EAAAA,CAAAA,GACNmE,KACCP,EAAAA,IAACN,GACC,EAAAG,aAAaA,GACbE,aAAaA,GACbH,gBAAgBA,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;IAK1B,GAAA,QAAA,gBAAA;;;", "names": ["reducer", "state", "action", "type", "__assign", "speed", "text", "_a", "payload", "substring", "length", "_b", "count", "useTypewriter", "words", "_c", "loop", "_d", "typeSpeed", "_e", "deleteSpeed", "_f", "delaySpeed", "onLoopDone", "onType", "onDelete", "onDelay", "_g", "useReducer", "_h", "dispatch", "loops", "useRef", "isDone", "isDelete", "isType", "isDelay", "handleTyping", "useCallback", "index", "fullWord", "current", "setTimeout", "useEffect", "typing", "clearTimeout", "css", "ref", "insertAt", "document", "head", "getElementsByTagName", "style", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "styleSheet", "cssText", "createTextNode", "<PERSON><PERSON><PERSON>", "memo", "cursorBlinking", "cursorStyle", "u", "cursorColor", "_jsx", "color", "className", "concat", "styles", "children", "r", "cursor", "_j", "_k", "_jsxs", "_Fragment"]}