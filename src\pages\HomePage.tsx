import { motion } from 'framer-motion'
import { 
  ArrowRight, BookOpen, Users, Award, Clock, Code, CheckCircle, 
  Terminal, Server, Database, Shield, Book, User, Target,
  CheckCircle2, BookCheck, Zap, Github
} from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import CodeTypingEffect from '../components/CodeTypingEffect'
import AleksandrePhoto from '../assets/aleksandre.jpg'

const HomePage = () => {
  const navigate = useNavigate()

  const handleBookSession = () => {
    navigate('/contact')
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }



  return (
    <div className="space-y-20">
      {/* Hero Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
            <div className="w-full md:w-1/2 mb-6 md:mb-0">
              <motion.h1 
                className="text-4xl md:text-5xl font-bold mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                Master{' '}
                <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 bg-clip-text text-transparent bg-[length:200%_200%] animate-gradient">
                  C Programming
                </span>{' '}
                with Expert Guidance
              </motion.h1>
              <motion.p 
                className="text-xl text-gray-300 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Personalized tutoring sessions to help you understand the fundamentals <br /> and advanced concepts of C programming.
              </motion.p>
              <motion.div 
                className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <button onClick={handleBookSession} className="btn-primary flex items-center justify-center">
                  Book a Session
                  <ArrowRight className="ml-2 h-5 w-5" />
                </button>
                <Link to="/contact" className="btn-secondary flex items-center justify-center">
                  Learn More
                </Link>
              </motion.div>
            </div>
            <motion.div
              className="w-full md:w-1/2 min-w-0"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <CodeTypingEffect />
            </motion.div>
          </div>
        </div>
      </section>

      {/* What is C Used For Section */}
      <section className="py-16 bg-gradient-custom rounded-xl">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">What is C Used For and Why Learn It?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
            <div className="card">
              <Terminal className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">System Programming</h3>
              <p className="text-gray-300">
                C is the foundation of operating systems like Linux and Windows. Learn to write efficient system-level code.
              </p>
            </div>
            
            <div className="card">
              <Server className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Embedded Systems</h3>
              <p className="text-gray-300">
                Power IoT devices, microcontrollers, and real-time systems with C programming skills.
              </p>
            </div>
            
            <div className="card">
              <Database className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Performance Critical Apps</h3>
              <p className="text-gray-300">
                Build high-performance applications where speed and efficiency matter most.
              </p>
            </div>
            
            <div className="card">
              <Shield className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Security Software</h3>
              <p className="text-gray-300">
                Develop secure applications and understand low-level security concepts.
              </p>
            </div>
            
            <div className="card">
              <Code className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Foundation for Other Languages</h3>
              <p className="text-gray-300">
                Master C to better understand modern programming languages built upon its concepts.
              </p>
            </div>
            
            <div className="card">
              <Award className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Career Opportunities</h3>
              <p className="text-gray-300">
                Open doors to careers in systems programming, embedded development, and more.
              </p>
            </div>
          </div>
        </div>
      </section>

     {/* Profile Section */}
     <section className="py-16 bg-gradient-custom rounded-xl">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-2/3 space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-white">Meet Your C Programming Tutor</h2>
              <p className="text-xl text-gray-300">
                Hi I'm Alexandre, with extensive experience in C programming and a passion for teaching, I'm here to help you master the fundamentals and advanced concepts of C programming.
              </p>
              <a 
                href="https://github.com/AppleItsme/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-2 bg-accent hover:bg-accent-hover text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                <Github className="h-5 w-5" />
                <span>View My Portfolio</span>
              </a>
            </div>
            <div className="lg:w-1/3 w-full max-w-sm mx-auto lg:mx-0">
              <div className="relative">
                <motion.div 
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full aspect-[3/4] rounded-xl overflow-hidden border-2 border-accent/20 hover:border-accent/40 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <img 
                    src={AleksandrePhoto} 
                    alt="Aleksandre - C Programming Tutor"
                    className="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-300"
                  />
                </motion.div>
                {/* Decorative background element */}
                <div className="absolute -inset-4 bg-gradient-to-r from-accent/10 to-purple-500/10 rounded-xl -z-10 blur-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose My Tutoring Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">Why Choose My Tutoring Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
            <div className="card">
              <Book className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Comprehensive Curriculum</h3>
              <p className="text-gray-300">
                From basic syntax to advanced memory management, my curriculum covers all aspects of C programming.
              </p>
            </div>

            <div className="card">
              <User className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Personalized Approach</h3>
              <p className="text-gray-300">
                Tailored lessons based on your learning style, pace, and specific goals in C programming.
              </p>
            </div>

            <div className="card">
              <Target className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Industry Experience</h3>
              <p className="text-gray-300">
                Learn from a tutor with real-world experience in software development and C programming.
              </p>
            </div>

            <div className="card">
              <Clock className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Flexible Scheduling</h3>
              <p className="text-gray-300">
                Book sessions at times that work for you, with options for recurring or one-time lessons.
              </p>
            </div>

            <div className="card">
              <BookCheck className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Practical Projects</h3>
              <p className="text-gray-300">
                Reinforce your learning with hands-on projects that build your portfolio and confidence.
              </p>
            </div>

            <div className="card">
              <Zap className="h-12 w-12 text-accent mb-4" />
              <h3 className="text-xl font-semibold mb-3">Ongoing Support</h3>
              <p className="text-gray-300">
                Get help with questions and code reviews between sessions via email or messaging.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Topics I Cover */}
      <section className="py-16 bg-gradient-custom rounded-xl">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">Topics I Cover</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
            <div className="card">
              <h3 className="text-xl font-semibold mb-4 text-accent">Fundamentals</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>C Syntax and Basic Programming Concepts</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Data Types, Variables, and Operators</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Control Structures (if, switch, loops)</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Functions and Program Structure</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Arrays and Strings</span>
                </li>
              </ul>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold mb-4 text-accent">Intermediate</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Pointers and Memory Management</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Structures, Unions, and Enumerations</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Standard Library Functions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Dynamic Memory Allocation</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Command Line Arguments</span>
                </li>
              </ul>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold mb-4 text-accent">Advanced</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Advanced Pointer Concepts</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Bit Manipulation</span>
                </li>
              </ul>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold mb-4 text-accent">Practical Applications</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Data Structures Implementation</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Algorithms in C</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Debugging Techniques</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Performance Optimization</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-accent mr-2" />
                  <span>Building Real-world Applications</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      {/*<section id="pricing" className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">Tutoring Packages</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="card text-center hover:border-accent hover:border-2 transition-all duration-300">
              <h3 className="text-2xl font-bold mb-4">Basic Package</h3>
              <div className="text-4xl font-bold mb-6">
                <span className="text-accent">$45</span>
                <span className="text-xl text-gray-400">/hour</span>
              </div>
              <ul className="space-y-3 text-left mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>1-on-1 Personalized Sessions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Homework Help</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Basic Concept Coverage</span>
                </li>
              </ul>
              <button onClick={handleBookSession} className="btn-primary block w-full">Get Started</button>
            </div>

            <div className="card text-center transform scale-105 border-2 border-accent">
              <div className="absolute top-0 right-0 bg-accent text-white px-4 py-1 rounded-bl-lg rounded-tr-lg">
                Popular
              </div>
              <h3 className="text-2xl font-bold mb-4">Standard Package</h3>
              <div className="text-4xl font-bold mb-6">
                <span className="text-accent">$60</span>
                <span className="text-xl text-gray-400">/hour</span>
              </div>
              <ul className="space-y-3 text-left mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>All Basic Features</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Project Development</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Code Review Support</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Email Support</span>
                </li>
              </ul>
              <button onClick={handleBookSession} className="btn-primary block w-full">Get Started</button>
            </div>

            <div className="card text-center hover:border-accent hover:border-2 transition-all duration-300">
              <h3 className="text-2xl font-bold mb-4">Premium Package</h3>
              <div className="text-4xl font-bold mb-6">
                <span className="text-accent">$75</span>
                <span className="text-xl text-gray-400">/hour</span>
              </div>
              <ul className="space-y-3 text-left mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>All Standard Features</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Advanced Topics Coverage</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Priority Support</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-accent mr-2" />
                  <span>Interview Preparation</span>
                </li>
              </ul>
              <button onClick={handleBookSession} className="btn-primary block w-full">Get Started</button>
            </div>
          </div>
        </div>
      </section>
      */}
      {/* Ready to Master C Programming? (CTA) */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="card text-center py-16">
            <h2 className="text-4xl font-bold mb-4">Ready to Master C Programming?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Book your first session today and start your journey to becoming proficient in C programming with personalized guidance.
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <button onClick={handleBookSession} className="btn-primary">
                Book a Session
              </button>
              <Link to="/contact" className="btn-secondary">
                Contact Me
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage
