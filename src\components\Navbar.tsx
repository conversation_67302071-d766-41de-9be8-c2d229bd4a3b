import { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X } from 'lucide-react'
import qochiLogo from '../assets/qochi_logo.png'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const location = useLocation()

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 border-b border-blue-500/20 ${
      scrolled 
        ? 'bg-background/95 backdrop-blur-sm shadow-lg' 
        : 'bg-background/95 backdrop-blur-sm md:bg-transparent md:backdrop-blur-none'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link 
            to="/" 
            className="flex items-center space-x-2 group"
          >
             <img 
              src={qochiLogo} 
              alt="Qochi Logo" 
              className="h-12 w-12 transition-transform duration-300 group-hover:rotate-12" 
            /> 
            <span className="text-xl font-bold text-white hover:text-blue-400 transition-colors duration-300">
              Qochi Services
            </span>
          </Link>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className={`relative text-white hover:text-blue-400 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-400 after:transition-all after:duration-300 hover:after:w-full ${
                location.pathname === '/' ? 'text-blue-400 after:w-full' : ''
              }`}
            >
              Home
            </Link>
            <Link 
              to="/contact" 
              className={`relative text-white hover:text-blue-400 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-400 after:transition-all after:duration-300 hover:after:w-full ${
                location.pathname === '/contact' ? 'text-blue-400 after:w-full' : ''
              }`}
            >
              Contact
            </Link>
            <a 
              href="#pricing" 
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Book a Session
            </a>
          </div>
          
          {/* Mobile Navigation Button */}
          <div className="md:hidden">
            <button 
              onClick={() => setIsOpen(!isOpen)}
              className="text-white hover:text-blue-400 focus:outline-none transition-colors duration-300"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
        
        {/* Mobile Navigation Menu */}
        <div 
          className={`md:hidden transition-all duration-300 ease-in-out ${
            isOpen 
              ? 'max-h-64 opacity-100 visible' 
              : 'max-h-0 opacity-0 invisible'
          }`}
        >
          <div className="py-4 border-t border-blue-500/20 space-y-4">
            <Link 
              to="/" 
              className={`block text-white hover:text-blue-400 transition-colors duration-300 ${
                location.pathname === '/' ? 'text-blue-400' : ''
              }`}
              onClick={() => setIsOpen(false)}
            >
              Home
            </Link>
            <Link 
              to="/contact" 
              className={`block text-white hover:text-blue-400 transition-colors duration-300 ${
                location.pathname === '/contact' ? 'text-blue-400' : ''
              }`}
              onClick={() => setIsOpen(false)}
            >
              Contact
            </Link>
            <a 
              href="#pricing" 
              className="block bg-blue-500 hover:bg-blue-600 text-white text-center font-medium py-2 px-6 rounded-lg transition-all duration-300"
              onClick={() => setIsOpen(false)}
            >
              Book a Session
            </a>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
