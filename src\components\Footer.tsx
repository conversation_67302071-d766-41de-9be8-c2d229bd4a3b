import { Mail, Phone, MapPin, Code, Github, Linkedin, Twitter } from 'lucide-react'
import { Link } from 'react-router-dom'

const Footer = () => {
  const handleContactClick = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <footer className="bg-secondary pt-12 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          {/* About */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Code className="h-6 w-6 text-accent" />
              <h3 className="text-xl font-bold">Qochi services</h3>
            </div>
            <p className="text-gray-300 mb-4">
              Professional C programming tutoring services to help you master the fundamentals and advanced concepts of C programming.
            </p>
            <div className="flex space-x-4">
              <a href="https://github.com/AppleItsme/" className="text-gray-400 hover:text-accent transition-colors">
                <Github className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-accent transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-accent transition-colors">Home</Link>
              </li>
              <li>
                <Link 
                  to="/contact" 
                  className="text-gray-300 hover:text-accent transition-colors"
                  onClick={handleContactClick}
                >
                  Contact
                </Link>
              </li>
             
            </ul>
          </div>
          
          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-accent mt-0.5" />
                <span className="text-gray-300"><EMAIL></span>
              </li>
              {/* <li className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-accent mt-0.5" />
                <span className="text-gray-300">+****************</span>
              </li> */}
            </ul>
          </div>
        </div>
        
        {/* Copyright */}
        <div className="pt-8 border-t border-gray-700 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} Qochi Services. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
