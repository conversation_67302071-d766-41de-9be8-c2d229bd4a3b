@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

body {
  @apply bg-background text-text;
  font-family: 'Inter', sans-serif;
}

code {
  font-family: 'Fira Code', monospace;
}

@layer components {
  .btn-primary {
    @apply bg-accent hover:bg-accent-hover text-white font-medium py-2 px-6 rounded-custom transition-all duration-300 shadow-custom hover:scale-105;
  }
  
  .btn-secondary {
    @apply bg-secondary hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-custom transition-all duration-300 shadow-custom hover:scale-105;
  }
  
  .card {
    @apply bg-secondary rounded-xl p-6 shadow-custom hover:shadow-lg transition-all duration-300 hover:scale-[1.02];
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold mb-6 text-white;
  }
  
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-accent via-blue-400 to-accent bg-size-200 animate-gradient;
  }
  
  .input-field {
    @apply bg-gray-800 border border-gray-700 rounded-custom px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-accent text-white;
  }

  .nav-link {
    @apply text-white hover:text-blue-400 transition-all duration-300 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-400 after:transition-all after:duration-300 hover:after:w-full;
  }

  .code-block {
    @apply font-mono bg-gray-900 rounded-xl p-3 sm:p-4 md:p-6;
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
    -ms-overflow-style: auto;
    word-break: break-all;
    white-space: pre-wrap;
  }

  .code-block::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .code-block::-webkit-scrollbar-track {
    background: #1F2937;
    border-radius: 3px;
  }

  .code-block::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 3px;
  }

  .code-block::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
  }

  .animate-blink {
    animation: blink 1s step-end infinite;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 6s linear infinite;
}

.bg-size-200 {
  background-size: 200% 200%;
}

select.input-field {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
