import { useEffect, useRef } from 'react'
import { useTypewriter } from 'react-simple-typewriter'
import Prism from 'prismjs'
import 'prismjs/components/prism-c'
import 'prismjs/themes/prism-tomorrow.css'

const CodeTypingEffect = () => {
  const codeRef = useRef<HTMLElement>(null)
  
  const [text] = useTypewriter({
    words: [
      `#include <stdio.h>\n\nint main() {\n    printf("Hello, welcome to Qochi\\n");\n    printf("C Programming Services!\\n");\n\n    // Learn fundamentals to advanced concepts\n    for (int i = 1; i <= 5; i++) {\n        printf("Level %d: Mastering C\\n", i);\n    }\n\n    return 0;\n}`
    ],
    typeSpeed: 20,
    deleteSpeed: 50,
    loop: 1
  })

  useEffect(() => {
    if (codeRef.current) {
      Prism.highlightElement(codeRef.current)
    }
  }, [text])

  return (
    <div className="bg-secondary rounded-custom p-6 shadow-custom border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 max-w-full overflow-hidden h-80 md:h-96">
      <pre className="code-block relative whitespace-pre-wrap break-words h-full overflow-y-auto">
        <code ref={codeRef} className="language-c text-sm md:text-base">
          {text}
        </code>
      </pre>
    </div>
  )
}

export default CodeTypingEffect
