import { useState, useEffect } from 'react'
import { Mail, Phone, HelpCircle, Send, MessageSquare, Clock, Calendar, User,BookOpen } from 'lucide-react'

// Type declarations for Cal.com
declare global {
  interface Window {
    Cal: any;
  }
}

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    level: 'beginner',
    description: ''
  })

  const [charCount, setCharCount] = useState(0)
  const maxChars = 500

  useEffect(() => {
    // Try to load Cal.com inline embed, fallback to iframe if it fails
    const loadCalEmbed = () => {
      const script = document.createElement('script')
      script.src = 'https://app.cal.com/embed/embed.js'
      script.async = true
      script.onload = () => {
        try {
          if (window.Cal) {
            window.Cal("init", "30min", {origin:"https://app.cal.com"})
            window.Cal.ns = window.Cal.ns || {}
            window.Cal.ns["30min"] = window.Cal.ns["30min"] || function() {}
            window.Cal.ns["30min"]("inline", {
              elementOrSelector:"#my-cal-inline-30min",
              config: {"layout":"month_view"},
              calLink: "qochi-services/30min",
            })
            window.Cal.ns["30min"]("ui", {"hideEventTypeDetails":false,"layout":"month_view"})
          }
        } catch (error) {
          console.warn('Cal.com inline embed failed, using iframe fallback')
          showIframeFallback()
        }
      }
      script.onerror = () => {
        console.warn('Cal.com script failed to load, using iframe fallback')
        showIframeFallback()
      }
      document.head.appendChild(script)

      return script
    }

    const showIframeFallback = () => {
      const container = document.getElementById('my-cal-inline-30min')
      if (container) {
        container.innerHTML = `
          <iframe
            src="https://cal.com/qochi-services/30min"
            width="100%"
            height="700px"
            style="border: none; border-radius: 8px;"
            title="Schedule a session"
          ></iframe>
        `
      }
    }

    const script = loadCalEmbed()

    return () => {
      if (script && document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (name === 'description') {
      setCharCount(value.length)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
    alert('Thank you for your message! I will get back to you soon.')
    setFormData({ name: '', email: '', level: 'beginner', description: '' })
    setCharCount(0)
  }

  return (
    <div className="space-y-16">
      {/* Header */}
      <section className="py-8">
        <h1 className="section-title text-center">Get in Touch</h1>
        <p className="text-xl text-gray-300 text-center max-w-3xl mx-auto">
          Have questions about C programming tutoring? Reach out and I'll be happy to help you on your learning journey.
        </p>
      </section>

      {/* Contact Form and Info */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="card">
              <h2 className="text-2xl font-semibold mb-6">Send Me a Message</h2>
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                      Your Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="input-field pl-10"
                        placeholder="Enter your full name"
                      />
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="input-field pl-10"
                        placeholder="Enter your email address"
                      />
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="level" className="block text-sm font-medium text-gray-300 mb-1">
                      Your Level
                    </label>
                    <div className="relative">
                      <select
                        id="level"
                        name="level"
                        value={formData.level}
                        onChange={handleChange}
                        required
                        className="input-field pl-10"
                      >
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>
                      <BookOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-1">
                      Description of Your Problem/Requirements
                    </label>
                    <div className="relative">
                      <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        required
                        rows={5}
                        maxLength={maxChars}
                        className="input-field pl-10"
                        placeholder="Please describe what you'd like to learn or any specific problems you're facing..."
                      ></textarea>
                      <MessageSquare className="absolute left-3 top-4 h-5 w-5 text-gray-400" />
                      <div className="absolute bottom-2 right-2 text-sm text-gray-400">
                        {charCount}/{maxChars}
                      </div>
                    </div>
                  </div>
                  
                  <button type="submit" className="btn-primary w-full flex items-center justify-center">
                    <Send className="mr-2 h-5 w-5" />
                    Send Message
                  </button>
                </div>
              </form>
            </div>
            
            {/* Contact Information and Calendly */}
            <div className="space-y-8">
              {/* Contact Details */}
              <div className="card">
                <h2 className="text-2xl font-semibold mb-6">Contact Information</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Mail className="h-6 w-6 text-accent mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Email</h3>
                      <p className="text-gray-300"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Clock className="h-6 w-6 text-accent mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Response Time</h3>
                      <p className="text-gray-300">Usually within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Calendar className="h-6 w-6 text-accent mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Available Hours</h3>
                      <p className="text-gray-300">Monday - Friday: 9AM - 6PM EST</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Cal.com Integration */}
              <div className="card">
                <h2 className="text-2xl font-semibold mb-6">Schedule a Session</h2>
                <div
                  id="my-cal-inline-30min"
                  style={{ width: '100%', height: '700px', overflow: 'scroll' }}
                ></div>
                {/* Fallback iframe if inline embed fails */}
                <noscript>
                  <iframe
                    src="https://cal.com/qochi-services/30min"
                    width="100%"
                    height="700px"
                    style={{ border: 'none' }}
                    title="Schedule a session"
                  ></iframe>
                </noscript>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default ContactPage
